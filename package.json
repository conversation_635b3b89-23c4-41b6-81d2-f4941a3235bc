{"name": "skstore-microservices", "version": "1.0.0", "description": "SKStore Microservices Architecture with PM2 Process Management", "main": "ecosystem.config.js", "scripts": {"install:all": "npm install && cd apigateway && npm install && cd ../services/user && npm install && cd ../../..", "start": "pm2 start ecosystem.config.js", "stop": "pm2 stop ecosystem.config.js", "restart": "pm2 restart ecosystem.config.js", "reload": "pm2 reload ecosystem.config.js", "delete": "pm2 delete ecosystem.config.js", "logs": "pm2 logs", "monit": "pm2 monit", "status": "pm2 status", "start:dev": "pm2 start ecosystem.config.js --env development", "start:prod": "pm2 start ecosystem.config.js --env production", "restart:dev": "pm2 restart ecosystem.config.js --env development", "restart:prod": "pm2 restart ecosystem.config.js --env production", "save": "pm2 save", "resurrect": "pm2 resurrect", "startup": "pm2 startup", "unstartup": "pm2 unstartup", "flush": "pm2 flush", "reset": "pm2 reset all", "kill": "pm2 kill", "start:gateway": "pm2 start ecosystem.config.js --only api-gateway", "start:user": "pm2 start ecosystem.config.js --only user-service", "stop:gateway": "pm2 stop api-gateway", "stop:user": "pm2 stop user-service", "restart:gateway": "pm2 restart api-gateway", "restart:user": "pm2 restart user-service", "logs:gateway": "pm2 logs api-gateway", "logs:user": "pm2 logs user-service", "health": "curl -s http://localhost:8000/health && curl -s http://localhost:5001/health && curl -s http://localhost:5002/health && curl -s http://localhost:5003/health", "test:all": "cd apigateway && npm test && cd ../services/user &&  npm test && cd ../../..", "docs:install": "cd docs && npm install", "docs:start": "cd docs && npm start", "docs:build": "cd docs && npm run build", "docs:serve": "cd docs && npm run serve", "docs:deploy": "cd docs && npm run deploy", "setup:all": "npm run install:all && npm run docs:install", "dev:all": "concurrently \"npm start\" \"npm run docs:start\"", "build:all": "npm run docs:build"}, "keywords": ["microservices", "pm2", "nodejs", "express", "api-gateway", "user-service", "employee-service"], "author": "", "license": "ISC", "devDependencies": {"concurrently": "^9.1.2", "pm2": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "dependencies": {"serverless-http": "^3.2.0"}}