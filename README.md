# SKStore Microservices

A simplified microservices architecture with API Gateway and User Service.

## Architecture

This project consists of two main services:

1. **API Gateway** (`apigateway/`) - Port 8000
   - Handles authentication and routing
   - Provides unified API interface
   - Manages security and rate limiting

2. **User Service** (`services/user/`) - Port 5001
   - User management and authentication
   - User registration, login, logout
   - Password reset functionality

## Prerequisites

- Node.js (v14 or higher)
- MongoDB
- PM2 (for process management)

## Installation

1. Install dependencies:
```bash
npm install
cd apigateway && npm install
cd ../services/user && npm install
```

2. Set up environment variables:
   - Copy `.env.example` to `.env` in both `apigateway/` and `services/user/` directories
   - Configure MongoDB connection strings and JWT secrets

## Running the Services

### Using PM2 (Recommended)
```bash
# Start all services
./start-services.sh start

# Or use PM2 directly
pm2 start ecosystem.config.js
```

### Manual Start
```bash
# Start API Gateway
cd apigateway && npm start

# Start User Service (in another terminal)
cd services/user && npm start
```

## API Endpoints

### Authentication
- `POST /api/user/auth/login` - User login
- `POST /api/user/auth/logout` - User logout
- `POST /api/user/auth/forgot-password` - Request password reset
- `POST /api/user/auth/verify-reset-password` - Reset password with code

### User Management
- `POST /api/user/users` - Create user
- `GET /api/user/users/:id` - Get user by ID
- `PUT /api/user/users/:id` - Update user

### Health Checks
- `GET /health` - API Gateway health
- `GET /api/user/health` - User service health

## Environment Variables

### API Gateway
- `JWT_SECRET` - JWT signing secret
- `REFRESH_TOKEN_SECRET` - Refresh token secret
- `USER_SERVICE_URL` - User service URL (default: http://localhost:5001)

### User Service
- `MONGODB_URI` - MongoDB connection string
- `JWT_SECRET` - JWT signing secret
- `JWT_EXPIRES_IN` - Token expiration time (default: 24h)

## Development

### Project Structure
```
├── apigateway/          # API Gateway service
│   ├── api/
│   │   ├── middleware/  # Authentication, security, error handling
│   │   └── routes/      # Route definitions
│   └── app.js          # Main application file
├── services/
│   └── user/           # User service
│       ├── controller/ # Request handlers
│       ├── middleware/ # Authentication, validation
│       ├── models/     # Database models
│       ├── routes/     # API routes
│       ├── services/   # Business logic
│       └── app.js      # Main application file
├── ecosystem.config.js # PM2 configuration
├── start-services.sh   # Service management script
└── package.json        # Root dependencies
```

### Adding New Services

1. Create a new service directory in `services/`
2. Add service configuration to `ecosystem.config.js`
3. Update API Gateway routes to proxy to the new service
4. Update `start-services.sh` if needed

## Monitoring

- PM2 provides process monitoring and restart capabilities
- Health check endpoints available for each service
- Logs are stored in `logs/` directory

## Security Features

- JWT-based authentication
- Rate limiting
- CORS protection
- Helmet security headers
- Request size limiting
- IP blocking capabilities

## License

This project is licensed under the MIT License. 