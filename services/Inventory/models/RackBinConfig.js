const mongoose = require('mongoose');
const ObjectId = mongoose.Schema.Types.ObjectId;

/**
 * RackBinConfig Schema for Inventory Management System
 * Implements comprehensive rack and bin configuration with embedded documents
 */
const rackBinConfigSchema = new mongoose.Schema({
    _id: {
        type: ObjectId,
        required: true
    },
    rackBinConfigId: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,
        default: () => {
            return `RBC${Date.now().toString().substring(0, 10)}${Math.floor(Math.random() * 1000000000)}`;
        },
        index: true
    },
    franchiseId: {
        type: String,
        required: true,
        ref: 'Franchise',
        trim: true,
        maxlength: 50,
        index: true
    },
    location: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100,
        default: 'Sellable',
        index: true
    },
    locationId: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50,
        index: true
    },
    racks: [{
        rackId: {
            type: String,
            required: true,
            trim: true,
            maxlength: 50
        },
        rackNumber: {
            type: String,
            required: true,
            trim: true,
            maxlength: 20
        },
        bins: [{
            binId: {
                type: String,
                required: true,
                trim: true,
                maxlength: 50
            },
            binNumber: {
                type: String,
                required: true,
                trim: true,
                maxlength: 20
            },
            capacity: {
                type: Number,
                required: true,
                min: [1, 'Capacity must be at least 1'],
                default: 1
            },
            status: {
                type: String,
                required: true,
                enum: ['Active', 'Inactive', 'Maintenance'],
                default: 'Active'
            },
            createdAt: {
                type: Date,
                default: Date.now
            },
            createdBy: {
                type: String,
                ref: 'User',
                required: true
            }
        }],
        status: {
            type: String,
            required: true,
            enum: ['Active', 'Inactive', 'Maintenance'],
            default: 'Active'
        },
        createdAt: {
            type: Date,
            default: Date.now
        },
        createdBy: {
            type: String,
            ref: 'User',
            required: true
        }
    }],
    rackCount: {
        type: Number,
        required: true,
        min: [0, 'Rack count cannot be negative'],
        default: 0
    },
    status: {
        type: String,
        required: true,
        enum: ['Active', 'Inactive', 'Pending'],
        default: 'Pending',
        index: true
    },
    auditLog: [
        {
            action: {
                type: String,
                required: true
            },
            createdOn: {
                type: Date,
                default: Date.now
            },
            createdBy: {
                type: String,
                ref: 'User',
                required: true
            },
            remarks: {
                type: String,
                trim: true
            }
        }
    ],
    createdBy: {
        type: String,
        ref: 'User',
        required: true,
        index: true
    },
    createdAt: {
        type: Date,
        default: Date.now,
        index: true
    },
    modifiedBy: {
        type: String,
        ref: 'User'
    },
    modifiedAt: {
        type: Date,
        default: Date.now
    }
});

// Indexes for better query performance
rackBinConfigSchema.index({ rackBinConfigId: 1 });
rackBinConfigSchema.index({ franchiseId: 1, location: 1 });
rackBinConfigSchema.index({ franchiseId: 1, locationId: 1 });
rackBinConfigSchema.index({ status: 1, createdAt: 1 });
rackBinConfigSchema.index({ createdBy: 1, createdAt: 1 });

// Compound indexes
rackBinConfigSchema.index({ franchiseId: 1, location: 1, status: 1 });
rackBinConfigSchema.index({ franchiseId: 1, locationId: 1, status: 1 });

// Pre-save middleware to update rackCount and modifiedAt
rackBinConfigSchema.pre('save', function(next) {
    if (this.isModified('racks')) {
        this.rackCount = this.racks.length;
    }

    if (this.isModified() && !this.isNew) {
        this.modifiedAt = new Date();
    }

    next();
});

module.exports = mongoose.model('RackBinConfig', rackBinConfigSchema);