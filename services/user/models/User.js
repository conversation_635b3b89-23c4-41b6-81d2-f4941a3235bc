const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate User ID with US prefix
 */
const generateUserId = () => {
  return `US${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * User Schema for User Management System
 * Implements comprehensive user data structure with embedded address
 */
const userSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateUserId
  },
  type: {
    type: String,
    required: true,
    enum: ['Employee','Franchise'],
    default: 'Employee',
    index: true
  },
  password: {
    type: String,
    required: true
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
    index: true
  },
  username: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    maxlength: 50,
    index: true
  },
  mobileNo: {
    type: String,
    required: true,
    trim: true,
    match: [/^\d{10}$/, 'Mobile number must be exactly 10 digits'],
    index: true
  },
  email: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    lowercase: true,
    match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
  },
  address: {
    doorNo: {
      type: String,
      required: true,
      trim: true,
      maxlength: 20
    },
    street: {
      type: String,
      required: true,
      trim: true,
      maxlength: 100
    },
    city: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
      index: true
    },
    state: {
      type: String,
      required: true,
      trim: true,
      maxlength: 50,
      index: true
    },
    postcode: {
      type: String,
      required: true,
      trim: true,
      match: [/^\d{6}$/, 'Postcode must be exactly 6 digits']
    }
  },
  registeredOn: {
    type: Date,
    default: Date.now
  },
  lastLogin: {
    type: Date
  },
  twoFactorEnabled: {
    type: Boolean,
    default: false,
    index: true
  },
  // Password reset verification
  resetCode: {
    type: String,
    default: null
  },
  resetCodeExpiry: {
    type: Date,
    default: null
  },
  status: {
    type: String,
    required: true,
    enum: ['Active', 'Inactive', 'Suspended'],
    default: 'Active',
    index: true
  },
  createdBy: {
    type: String,
    ref: 'User',
    index: true
  },
  createdOn: {
    type: Date,
    default: Date.now,
    index: true
  },
  modifiedBy: {
    type: String,
    ref: 'User',
    index: true
  },
  modifiedOn: {
    type: Date,
    default: Date.now
  }
});

// Indexes for better query performance
// Note: email already has unique index from schema definition
userSchema.index({ userId: 1 });
userSchema.index({ type: 1, status: 1 });
userSchema.index({ 'address.city': 1, 'address.state': 1 });
userSchema.index({ registeredOn: 1 });
userSchema.index({ lastLogin: 1 });
userSchema.index({ createdBy: 1, createdOn: 1 });

// Compound indexes
userSchema.index({ type: 1, status: 1, registeredOn: 1 });
userSchema.index({ email: 1, status: 1 });

// Pre-save middleware to set username to mobileNo if not provided
userSchema.pre('save', function(next) {
  if (!this.username && this.mobileNo) {
    this.username = this.mobileNo;
  }
  next();
});

module.exports = mongoose.model('User', userSchema);
