const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate UserAuth ID
 */
const generateUserAuthId = () => {
  return uuidv4();
};

/**
 * UserAuth Schema for Session Management
 * Handles user authentication tokens and session tracking
 */
const userAuthSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateUserAuthId
  },
  userId: {
    type: String,
    required: true,
    ref: 'User'
  },
  loginFrom: {
    type: String,
    trim: true,
    maxlength: 100,
    index: true
  },
  token: {
    type: String,
    required: true,
    unique: true
  },
  expiresOn: {
    type: Date
  },
  ipAddress: {
    type: String,
    trim: true,
    match: [/^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/, 'Please enter a valid IP address'],
    index: true
  },
  loggedOn: {
    type: Date,
    required: true,
    default: Date.now,
    index: true
  }
});

// Indexes for better query performance
// Note: token already has unique index from schema definition
userAuthSchema.index({ userId: 1, expiresOn: 1 });
userAuthSchema.index({ expiresOn: 1 }); // For cleanup of expired tokens
userAuthSchema.index({ userId: 1, loggedOn: -1 }); // For recent sessions
userAuthSchema.index({ ipAddress: 1, loggedOn: -1 }); // For security monitoring

// Compound indexes
userAuthSchema.index({ userId: 1, loginFrom: 1, loggedOn: -1 });
userAuthSchema.index({ userId: 1, expiresOn: 1, loggedOn: -1 });

// Instance methods
userAuthSchema.methods.isExpired = function() {
  return new Date() > this.expiresOn;
};

userAuthSchema.methods.extendExpiry = function(hours = 24) {
  this.expiresOn = new Date(Date.now() + (hours * 60 * 60 * 1000));
  return this.save();
};

userAuthSchema.methods.revoke = function() {
  this.expiresOn = new Date(); // Set expiry to now
  return this.save();
};

// Static methods
userAuthSchema.statics.findValidToken = function(token) {
  return this.findOne({
    token: token,
    expiresOn: { $gt: new Date() }
  }).populate('userId');
};

userAuthSchema.statics.findUserSessions = function(userId) {
  return this.find({
    userId: userId,
    expiresOn: { $gt: new Date() }
  }).sort({ loggedOn: -1 });
};

userAuthSchema.statics.revokeUserSessions = function(userId, exceptToken = null) {
  const query = { userId: userId };
  if (exceptToken) {
    query.token = { $ne: exceptToken };
  }
  
  return this.updateMany(query, {
    $set: { expiresOn: new Date() }
  });
};

userAuthSchema.statics.cleanupExpiredTokens = function() {
  return this.deleteMany({
    expiresOn: { $lt: new Date() }
  });
};

userAuthSchema.statics.findByIpAddress = function(ipAddress, limit = 10) {
  return this.find({
    ipAddress: ipAddress,
    expiresOn: { $gt: new Date() }
  })
  .sort({ loggedOn: -1 })
  .limit(limit)
  .populate('userId', 'name email type');
};

// Transform output
userAuthSchema.set('toJSON', {
  transform: function(doc, ret) {
    delete ret.__v;
    delete ret.token; // Don't expose token in JSON output for security
    return ret;
  }
});

module.exports = mongoose.model('UserAuth', userAuthSchema);
