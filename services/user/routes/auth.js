const express = require('express');
const router = express.Router();

// Import middleware
const authMiddleware = require('../middleware/auth');
const userValidation = require('../validation/userValidation');

// Import controller
const userController = require('../controller/userController');

/**
 * @route   POST /auth/login
 * @desc    User login
 * @access  Public
 */
router.post('/login',
  userValidation.validateLoginMiddleware,
  userController.login
);




module.exports = router;
