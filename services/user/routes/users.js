const express = require('express');
const router = express.Router();

// Import middleware
// const authMiddleware = require('../middleware/auth');

// Import controllers
const userController = require('../controller/userController');

/**
 * @route   POST /users
 * @desc    Create a new user
 * @access  Private
 */
router.post('/',
  // authMiddleware.authenticate(),
  userController.createUser
);

/**
 * @route   PUT /users/:id
 * @desc    Update an existing user
 * @access  Private (Admin or own profile)
 */
router.put('/:id',
  // authMiddleware.authenticate(),
  userController.updateUser
);

/**
 * @route   GET /users/:id
 * @desc    Get user by ID
 * @access  Private (Admin or own profile)
 */
router.get('/:id',
  // authMiddleware.authenticate(),
  userController.getUserById
);

module.exports = router;
