const jwt = require('jsonwebtoken');
const User = require('../models/User');
const UserAuth = require('../models/UserAuth');

// JWT Configuration with fallback
const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';

/**
 * Authentication Middleware for User Service
 * Provides JWT token validation and error handling
 */

/**
 * Generate JWT token
 */
const generateToken = (payload, expiredIn) => {
  return jwt.sign(payload, jwtSecret, { expiresIn: expiredIn || jwtExpiresIn });
};

/**
 * Generate password reset token
 */
const generatePasswordResetToken = (payload) => {
  return jwt.sign(payload, jwtSecret, { expiresIn: '1h' }); // 1 hour expiry for reset tokens
};

/**
 * Verify JWT token
 */
const verifyToken = (token) => {
  console.log('Verifying token:', jwtSecret);
  try {
    return jwt.verify(token, jwtSecret);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

/**
 * Extract token from request headers
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return null;

  if (authHeader.startsWith('JWT ')) {
    return authHeader.substring(4);
  }

  return authHeader;
};

/**
 * Extract user context from API Gateway headers
 */
const extractUserFromHeaders = (req) => {
  const userId = req.headers['x-user-id'];
  const userType = req.headers['x-user-type'];
  const userName = req.headers['x-user-name'];
  const userEmail = req.headers['x-user-email'];
  const userStatus = req.headers['x-user-status'];

  if (!userId) {
    return null;
  }

  return {
    id: userId,
    type: userType || 'Employee', // Default type
    name: userName || 'Unknown User',
    email: userEmail || '',
    status: userStatus || 'Active' // Default status
  };
};

/**
 * Authenticate user with JWT token or API Gateway context
 * When called from API Gateway, user context is passed via headers
 * When called directly, falls back to JWT token validation
 */
const authenticate = () => {
  return async (req, res, next) => {
    try {
      // First, check if user context is provided by API Gateway
      const gatewayUser = extractUserFromHeaders(req);

      if (gatewayUser) {
        console.log(`[${req.requestId}] Using API Gateway user context for user: ${gatewayUser.id}`);

        // User authenticated by API Gateway, use the provided context directly
        // This avoids database lookup since API Gateway already validated the user
        req.user = {
          id: gatewayUser.id,
          userId: gatewayUser.id, // Use same ID for userId
          name: gatewayUser.name,
          email: gatewayUser.email,
          type: gatewayUser.type,
          status: gatewayUser.status
        };

        return next();
      }

      console.log(`[${req.requestId}] No API Gateway context, falling back to JWT token validation`);

      // Fallback to JWT token validation for direct service calls
      const token = extractToken(req);

      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          message: 'No token provided',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }

      // Verify token
      const decoded = verifyToken(token);
      console.log('Decoded token:', JSON.stringify(decoded, null, 2));

      // Check if token exists in UserAuth and is not expired
      // Skip UserAuth check for internal service calls (when User-Agent contains service name)
      const userAgent = req.headers['user-agent'] || '';
      const isInternalCall = userAgent.includes('axios') || userAgent.includes('node');

      if (!isInternalCall) {
        const userAuth = await UserAuth.findValidToken(token);
        if (!userAuth) {
          return res.status(401).json({
            success: false,
            error: 'Invalid token',
            message: 'Token not found or expired',
            requestId: req.requestId,
            timestamp: new Date().toISOString()
          });
        }
      }

      // Get user details
      console.log('Looking for user with ID:', decoded.id);
      const user = await User.findById(decoded.id);
      console.log('User found:', user ? 'YES' : 'NO');
      if (user) {
        console.log('User status:', user.status);
      }

      if (!user || user.status !== 'Active') {
        console.log('User lookup failed - user:', !!user, 'status:', user?.status);
        return res.status(401).json({
          success: false,
          error: 'User not found or inactive',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }

      // Attach user and token info to request
      req.user = {
        id: user._id,
        userId: user.userId,
        name: user.name,
        email: user.email,
        type: user.type,
        status: user.status,
        twoFactorEnabled: user.twoFactorEnabled
      };
      req.token = token;
      if (!isInternalCall) {
        const userAuth = await UserAuth.findValidToken(token);
        req.userAuth = userAuth;
      }

      next();
    } catch (error) {
      console.error(`[${req.requestId}] Authentication error:`, error);

      return res.status(401).json({
        success: false,
        error: 'Authentication failed',
        message: error.message,
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }
  };
};

/**
 * Optional authentication - doesn't fail if no token provided
 * Supports both API Gateway context and direct JWT token validation
 */
const optionalAuthenticate = () => {
  return async (req, _res, next) => {
    try {
      // First, check if user context is provided by API Gateway
      const gatewayUser = extractUserFromHeaders(req);

      if (gatewayUser) {
        console.log(`[${req.requestId}] Using API Gateway user context for optional auth: ${gatewayUser.id}`);

        // User authenticated by API Gateway, use the provided context directly
        req.user = {
          id: gatewayUser.id,
          userId: gatewayUser.id, // Use same ID for userId
          name: gatewayUser.name,
          email: gatewayUser.email,
          type: gatewayUser.type,
          status: gatewayUser.status
        };

        return next();
      }

      // Fallback to JWT token validation
      const token = extractToken(req);

      if (!token) {
        return next();
      }

      const decoded = verifyToken(token);
      const userAuth = await UserAuth.findValidToken(token);

      if (userAuth) {
        const user = await User.findById(decoded.id);
        if (user && user.status === 'Active') {
          req.user = {
            id: user._id,
            userId: user.userId,
            name: user.name,
            email: user.email,
            type: user.type,
            status: user.status,
            twoFactorEnabled: user.twoFactorEnabled
          };
          req.token = token;
          req.userAuth = userAuth;
        }
      }

      next();
    } catch (error) {
      // For optional auth, we don't fail on errors
      console.warn(`[${req.requestId}] Optional authentication warning:`, error.message);
      next();
    }
  };
};

/**
 * Require specific user type
 */
const requireType = (allowedTypes) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    const userTypes = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];
    
    if (!userTypes.includes(req.user.type)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: `Required user type: ${userTypes.join(' or ')}`,
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Require admin access
 */
const requireAdmin = () => {
  return requireType('admin');
};

/**
 * Require admin or employee access
 */
const requireAdminOrEmployee = () => {
  return requireType(['admin', 'employee']);
};

/**
 * Check if user can access specific user data
 */
const canAccessUser = () => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    const targetUserId = req.params.id || req.params.userId;
    
    // Admin can access all user data
    if (req.user.type === 'admin') {
      return next();
    }

    // Users can only access their own data
    if (req.user.id === targetUserId || req.user.userId === targetUserId) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: 'Access denied',
      message: 'You can only access your own user data',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  };
};

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = () => {
  const attempts = new Map();
  const maxAttempts = 5;
  const windowMs = 15 * 60 * 1000; // 15 minutes

  return (req, res, next) => {
    const key = req.ip + ':' + (req.body.email || req.body.mobileNo || 'unknown');
    const now = Date.now();
    
    if (!attempts.has(key)) {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const attempt = attempts.get(key);
    
    if (now > attempt.resetTime) {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (attempt.count >= maxAttempts) {
      return res.status(429).json({
        success: false,
        error: 'Too many authentication attempts',
        message: 'Please try again later',
        retryAfter: Math.ceil((attempt.resetTime - now) / 1000),
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    attempt.count++;
    next();
  };
};

module.exports = {
  generateToken,
  generatePasswordResetToken,
  verifyToken,
  authenticate,
  optionalAuthenticate,
  requireType,
  requireAdmin,
  requireAdminOrEmployee,
  canAccessUser,
  authRateLimit
};
