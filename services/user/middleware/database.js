const mongoose = require('mongoose');
require('dotenv').config({ path: require('path').join(__dirname, '..', '.env') });

let isConnected = false;
const connectionString = process.env.MONGODB_URI;


const connect = () => {
  return new Promise((resolve, reject) => {
    if (isConnected) {
      console.log('Database already connected');
      return resolve();
    }

    const options = {
      maxPoolSize: 10,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
      bufferCommands: false
    };

    mongoose.connect(connectionString, options)
      .then(() => {
        isConnected = true;
        console.log('✅ Successfully connected to MongoDB');

        // Handle connection events
        mongoose.connection.on('error', (err) => {
          console.error('❌ MongoDB connection error:', err);
          isConnected = false;
        });

        mongoose.connection.on('disconnected', () => {
          console.log('⚠️ MongoDB disconnected');
          isConnected = false;
        });

        mongoose.connection.on('reconnected', () => {
          console.log('✅ MongoDB reconnected');
          isConnected = true;
        });

        resolve();
      })
      .catch((error) => {
        console.error('❌ Unable to connect to MongoDB:', error);
        isConnected = false;
        reject(error);
      });
  });
};

const disconnect = () => {
  return new Promise((resolve, reject) => {
    mongoose.disconnect()
      .then(() => {
        isConnected = false;
        console.log('✅ Disconnected from MongoDB');
        resolve();
      })
      .catch((error) => {
        console.error('❌ Error disconnecting from MongoDB:', error);
        reject(error);
      });
  });
};

const getConnectionStatus = () => {
  return {
    isConnected: isConnected,
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    name: mongoose.connection.name
  };
};

// Transaction support removed for MongoDB 6 compatibility

// Legacy function for backward compatibility
const connectDb = async () => {
  try {
    await connect();
  } catch (error) {
    console.error("Unable to connect MongoDB", error);
    process.exit(1);
  }
};

module.exports = {
  connect,
  disconnect,
  getConnectionStatus,
  connectDb // Keep for backward compatibility
};
