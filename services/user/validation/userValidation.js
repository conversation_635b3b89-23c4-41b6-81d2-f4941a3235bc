const Joi = require('joi');


const validateUserCreation = (data) => {
  const schema = Joi.object({
    type: Joi.string()
      .valid('Employee', 'Franchise')
      .required()
      .messages({
        'string.empty': 'Type is required',
        'any.only': 'Type must be one of: employee, franchise'
      }),
    name: Joi.string()
      .trim()
      .max(100)
      .required()
      .messages({
        'string.empty': 'Name is required',
        'string.max': 'Name cannot exceed 100 characters'
      }),
    mobileNo: Joi.string()
      .pattern(/^\d{10}$/)
      .required()
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits',
        'string.empty': 'Mobile number is required'
      }),
    password: Joi.string()
      .min(8)
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters',
        'string.empty': 'Password is required'
      }),
    email: Joi.string()
      .email()
      .lowercase()
      .required()
      .messages({
        'string.email': 'Please enter a valid email address',
        'string.empty': 'Email is required'
      }),
    address: Joi.object({
      doorNo: Joi.string()
        .trim()
        .max(20)
        .required()
        .messages({
          'string.empty': 'Door number is required',
          'string.max': 'Door number cannot exceed 20 characters'
        }),
      street: Joi.string()
        .trim()
        .max(100)
        .required()
        .messages({
          'string.empty': 'Street is required',
          'string.max': 'Street cannot exceed 100 characters'
        }),
      city: Joi.string()
        .trim()
        .max(50)
        .required()
        .messages({
          'string.empty': 'City is required',
          'string.max': 'City cannot exceed 50 characters'
        }),
      state: Joi.string()
        .trim()
        .max(50)
        .required()
        .messages({
          'string.empty': 'State is required',
          'string.max': 'State cannot exceed 50 characters'
        }),
      postcode: Joi.string()
        .pattern(/^\d{6}$/)
        .required()
        .messages({
          'string.pattern.base': 'Postcode must be exactly 6 digits',
          'string.empty': 'Postcode is required'
        })
    })
    .required()
    .messages({
      'any.required': 'Address is required'
    }),
    twoFactorEnabled: Joi.boolean()
      .default(false)
      .messages({
        'boolean.base': 'Two-factor authentication must be true or false'
      })
  });

  return schema.validate(data);
};

// Middleware function for Express routes
const validateUserCreationMiddleware = (req, res, next) => {
  const { error, value } = validateUserCreation(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};


/**
 * Validate user update data (excludes password field)
 */
const validateUserUpdate = (data) => {
  const schema = Joi.object({
    type: Joi.string()
      .valid('Employee', 'Franchise')
      .messages({
        'string.empty': 'Type is required',
        'any.only': 'Type must be one of: Employee, Franchise'
      }),
    name: Joi.string()
      .trim()
      .max(100)
      .messages({
        'string.empty': 'Name is required',
        'string.max': 'Name cannot exceed 100 characters'
      }),
    mobileNo: Joi.string()
      .pattern(/^\d{10}$/)
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits',
        'string.empty': 'Mobile number is required'
      }),
    email: Joi.string()
      .email()
      .lowercase()
      .messages({
        'string.email': 'Please enter a valid email address',
        'string.empty': 'Email is required'
      }),
    address: Joi.object({
      doorNo: Joi.string()
        .trim()
        .max(20)
        .messages({
          'string.empty': 'Door number is required',
          'string.max': 'Door number cannot exceed 20 characters'
        }),
      street: Joi.string()
        .trim()
        .max(100)
        .messages({
          'string.empty': 'Street is required',
          'string.max': 'Street cannot exceed 100 characters'
        }),
      city: Joi.string()
        .trim()
        .max(50)
        .messages({
          'string.empty': 'City is required',
          'string.max': 'City cannot exceed 50 characters'
        }),
      state: Joi.string()
        .trim()
        .max(50)
        .messages({
          'string.empty': 'State is required',
          'string.max': 'State cannot exceed 50 characters'
        }),
      postcode: Joi.string()
        .pattern(/^\d{6}$/)
        .messages({
          'string.pattern.base': 'Postcode must be exactly 6 digits',
          'string.empty': 'Postcode is required'
        })
    }).messages({
      'any.required': 'Address is required'
    }),
    status: Joi.string()
      .valid('Active', 'Inactive', 'Suspended')
      .messages({
        'any.only': 'Status must be one of: Active, Inactive, Suspended'
      }),
    twoFactorEnabled: Joi.boolean()
  });

  return schema.validate(data);
};

// Middleware function for Express routes
const validateUserUpdateMiddleware = (req, res, next) => {
  const { error, value } = validateUserUpdate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};

/**
 * Validate login data
 */

// Combined validation + middleware
const validateLoginMiddleware = (req, res, next) => {
  const schema = Joi.object({
    mobileNo: Joi.string()
      .pattern(/^\d{10}$/)
      .required()
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits',
        'string.empty': 'Mobile number is required'
      }),
    password: Joi.string()
      .min(1)
      .required()
      .messages({
        'string.empty': 'Password is required'
      })
  });

  const { error, value } = schema.validate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
    });
  }

  req.validatedData = value;
  next();
};

/**
 * Validate forgot password data
 */
const validateForgotPasswordMiddleware = (req, res, next) => {
  const schema = Joi.object({
    mobileNo: Joi.string()
      .pattern(/^\d{10}$/)
      .required()
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits',
        'string.empty': 'Mobile number is required'
      })
  });

  const { error, value } = schema.validate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  req.validatedData = value;
  next();
};

/**
 * Validate verify code and reset password data
 */
const validateVerifyCodeAndResetPasswordMiddleware = (req, res, next) => {
  const schema = Joi.object({
    mobileNo: Joi.string()
      .pattern(/^\d{10}$/)
      .required()
      .messages({
        'string.pattern.base': 'Mobile number must be exactly 10 digits',
        'string.empty': 'Mobile number is required'
      }),
    verificationCode: Joi.string()
      .pattern(/^\d{6}$/)
      .required()
      .messages({
        'string.pattern.base': 'Verification code must be exactly 6 digits',
        'string.empty': 'Verification code is required'
      }),
    newPassword: Joi.string()
      .min(8)
      .required()
      .messages({
        'string.min': 'Password must be at least 8 characters',
        'string.empty': 'New password is required'
      })
  });

  const { error, value } = schema.validate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  req.validatedData = value;
  next();
};



module.exports = {
  validateUserCreation,
  validateUserCreationMiddleware,
  validateUserUpdate,
  validateUserUpdateMiddleware,
  validateLoginMiddleware,
  validateForgotPasswordMiddleware,
  validateVerifyCodeAndResetPasswordMiddleware
};