const database = require('./middleware/database');
const User = require('./models/User');
const bcrypt = require('bcryptjs'); // Using bcryptjs instead of bcrypt

async function resetCustomerPassword() {
  try {
    await database.connect();
    console.log('Connected to database');
    
    const customer = await User.findById('USD8FE');
    console.log('Found customer:', customer.name);
    
    // Hash new password
    const newPassword = 'customer123';
    const hashedPassword = await bcrypt.hash(newPassword, 10);
    
    // Update password
    customer.password = hashedPassword;
    await customer.save();
    
    console.log('✅ Password updated successfully');
    console.log('New password:', newPassword);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    process.exit(0);
  }
}

resetCustomerPassword();
