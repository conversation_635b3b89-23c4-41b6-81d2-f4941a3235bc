# User Management Service

A comprehensive user management microservice built with Node.js, Express, and MongoDB. This service provides complete user lifecycle management with JWT authentication, role-based access control, and RESTful APIs.

## 🚀 Features

### Core Functionality
- **User Management**: Complete CRUD operations for user accounts
- **Authentication**: JWT-based authentication with session management
- **Authorization**: Role-based access control (user, employee, admin, customer)
- **User Profiles**: Comprehensive user profiles with embedded address objects
- **Search & Filtering**: Advanced search and filtering capabilities
- **Pagination**: Efficient pagination for large datasets

### Security Features
- **JWT Authentication**: Secure token-based authentication
- **Session Management**: Track and manage user sessions
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive data validation using Joi
- **Security Headers**: CORS, Helmet, and other security middleware
- **Password Security**: Bcrypt hashing with configurable salt rounds

### Monitoring & Observability
- **Health Checks**: Comprehensive health monitoring endpoints
- **Request Logging**: Detailed request/response logging
- **Performance Monitoring**: Track slow requests and performance metrics
- **Error Handling**: Centralized error handling with proper logging
- **Metrics**: Business and technical metrics endpoints

### Database Features
- **MongoDB Integration**: Mongoose ODM with advanced schema design
- **Transactions**: Database transactions for data consistency
- **Indexing**: Optimized database indexes for performance
- **Connection Pooling**: Efficient database connection management

## 📋 Prerequisites

- Node.js 18+ (recommended: 22.x LTS)
- MongoDB 4.4+
- npm or yarn

## 🛠️ Installation

1. **Navigate to the user service directory**
   ```bash
   cd services/user
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the service**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=5001
NODE_ENV=development

# Database Configuration
MONGODB_URI=your-mongodb-connection-string

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Service Configuration
SERVICE_NAME=user-service
SERVICE_VERSION=1.0.0
```

## 📚 API Documentation

### Authentication Endpoints

**Via API Gateway:**
- `POST /api/user/auth/login` - User login
- `POST /api/user/auth/logout` - User logout
- `GET /api/user/auth/me` - Get current user profile
- `POST /api/user/auth/refresh` - Refresh authentication token

**Direct Service Access:**
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user profile
- `POST /auth/refresh` - Refresh authentication token

### User Management Endpoints

**Via API Gateway:**
- `POST /api/user/users` - Create new user (Admin only)
- `GET /api/user/users` - Get users with pagination and filtering
- `GET /api/user/users/:id` - Get user by ID
- `PUT /api/user/users/:id` - Update user
- `DELETE /api/user/users/:id` - Delete user (Admin only)
- `GET /api/user/users/search` - Search users
- `GET /api/user/users/stats` - Get user statistics (Admin only)

**Direct Service Access:**
- `POST /users` - Create new user (Admin only)
- `GET /users` - Get users with pagination and filtering
- `GET /users/:id` - Get user by ID
- `PUT /users/:id` - Update user
- `DELETE /users/:id` - Delete user (Admin only)
- `GET /users/search` - Search users
- `GET /users/stats` - Get user statistics (Admin only)

### Session Management

**Via API Gateway:**
- `GET /api/user/users/:id/sessions` - Get user sessions
- `POST /api/user/users/:id/revoke-sessions` - Revoke user sessions

**Direct Service Access:**
- `GET /users/:id/sessions` - Get user sessions
- `POST /users/:id/revoke-sessions` - Revoke user sessions

### Health & Monitoring

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe
- `GET /metrics` - Service metrics

## 🗄️ Database Schema

### Users Collection

```javascript
{
  _id: "US[22-char-uuid]",           // Primary key with US prefix
  type: "user|employee|admin|customer",
  userId: "ObjectId",                // Reference/foreign key
  name: "String",                    // Full name
  mobileNo: "String",                // 10-digit mobile number
  email: "String",                   // Unique email address
  address: {
    doorNo: "String",                // Door/house number
    street: "String",                // Street name
    city: "String",                  // City name
    state: "String",                 // State/province
    postcode: "String"               // 6-digit postal code
  },
  registeredOn: "Date",              // Registration timestamp
  lastLogin: "Date",                 // Last login timestamp
  twoFactorEnabled: "Boolean",       // 2FA status
  status: "Active|Inactive|Suspended",
  createdBy: "ObjectId",             // Reference to creator
  createdOn: "Date",                 // Creation timestamp
  modifiedBy: "ObjectId",            // Reference to modifier
  modifiedOn: "Date"                 // Last modification timestamp
}
```

### UserAuth Collection

```javascript
{
  _id: "ObjectId",                   // Primary key
  userId: "ObjectId",                // Foreign key to Users._id
  loginFrom: "String",               // Login source/device
  token: "String",                   // Authentication token
  expiresOn: "Date",                 // Token expiration
  ipAddress: "String",               // IP address of login
  loggedOn: "Date"                   // Login timestamp
}
```

## 🔒 Security

- JWT tokens with configurable expiration
- Rate limiting on authentication endpoints
- Input validation and sanitization
- Security headers (CORS, Helmet, etc.)
- Password hashing with bcrypt
- Session management and tracking

## 🧪 Testing

```bash
# Run tests
npm test

# Run tests in watch mode
npm run test:watch
```

## 📊 Monitoring

The service provides comprehensive monitoring capabilities:

- Health check endpoints for container orchestration
- Request/response logging with unique request IDs
- Performance monitoring for slow requests
- Business metrics (user counts, session statistics)
- Error tracking and logging

## 🚀 Deployment

The service is designed for containerized deployment and includes:

- Health check endpoints for Kubernetes
- Graceful shutdown handling
- Environment-based configuration
- Serverless compatibility (AWS Lambda)

## 🤝 Integration

This service integrates with:

- **API Gateway**: Routes requests and handles load balancing
- **Employee Service**: Shares user authentication patterns
- **Frontend Applications**: Provides user management APIs

## 📝 License

ISC License
