const User = require('../models/User');
const UserAuth = require('../models/UserAuth');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const franchise = require('../models/franchise')
const userAuth=require('../models/UserAuth')


const loginUser = async (loginData) => {
  return new Promise(async (resolve, reject) => {
    try {
      const externalLoginPayload = {
        username: loginData.mobileNo,
        password: loginData.password,
        appType: "Retailer",
        userType: "Franchise",
        imeiNumber: "",
        appVersion: "v3"
      };
      const response = await axios.post('https://uat.storeking.in/apigateway/api/user/v1/login', externalLoginPayload, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 30000 // 30 seconds timeout
      });
      if (response) {
        const franchiseRes = await axios.get(`https://uat.storeking.in/apigateway/api/franchise/v1/${response.data.franchise}`, {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `JWT ${response.data.token}`
          },
          timeout: 30000
        })
        if (franchiseRes) {
          await  saveFranchises(franchiseRes.data).catch(err=>{
            reject(err)
          })
        }
        await saveOrUpdateUserToken(response.data._id,response.data.token).catch(err=>{
          reject(err)
        })
      }
      resolve(response.data);
    } catch (error) {
      reject(error);
    }
  });
};


async function saveFranchises(response) {
  return new Promise(async (resolve, reject) => {
    const result = await franchise.find({ franchiseId: response._id })
    if (result.length > 0) {
      resolve(result)
    } else {
      const owner = (response.contact_details && response.contact_details[0]) || {};
      const finance = response.finance_details || {};
      const address = response.address || {};
      const geo = (response.shop_details && response.shop_details.geolocation && response.shop_details.geolocation.coordinates) || [];
      const latitude = geo[1] || null;
      const longitude = geo[0] || null;

      const documents = [];
      if (response.sk_franchise_details?.mandatory_documents?.address) {
        response.sk_franchise_details.mandatory_documents.address.forEach(doc => {
          documents.push({
            type: 'Aadhar',
            refNumber: doc.addressProofNo,
            fileUrl: doc.addressProofFile,
            issuedBy: doc.approvedBy,
            issuedDate: doc.approvedOn,
            verified: doc.addressProofIdStatus === 'Approved',
            uploadedOn: doc.uploadedOn
          });
        });
      }
      // PAN
      if (response.sk_franchise_details?.mandatory_documents?.photo) {
        response.sk_franchise_details.mandatory_documents.photo.forEach(doc => {
          documents.push({
            type: 'PAN',
            refNumber: doc.photoIDNo,
            fileUrl: doc.photoIDFile,
            issuedBy: doc.approvedBy,
            issuedDate: doc.approvedOn,
            verified: doc.photoIdStatus === 'Approved',
            uploadedOn: doc.uploadedOn
          });
        });
      }
      // GST
      if (response.sk_franchise_details?.mandatory_documents?.business) {
        response.sk_franchise_details.mandatory_documents.business.forEach(doc => {
          documents.push({
            type: 'GST',
            refNumber: doc.businessIDNo,
            fileUrl: doc.businessIDFile,
            issuedBy: doc.approvedBy,
            issuedDate: doc.approvedOn,
            verified: doc.businessIDStatus === 'Approved',
            uploadedOn: doc.uploadedOn
          });
        });
      }
      // Shop Photos
      if (response.shop_photos_details) {
        response.shop_photos_details.forEach(doc => {
          documents.push({
            type: 'ShopPhoto',
            fileUrl: doc.name,
            issuedBy: doc.approvedBy,
            issuedDate: doc.approvedOn,
            verified: doc.status === 'Approved',
            uploadedOn: doc.uploadedOn
          });
        });
      }

      // Map status logs
      const statusLogs = [];
      if (response.manualStatusUpdateLog) {
        response.manualStatusUpdateLog.forEach(log => {
          statusLogs.push({
            status: log.status,
            updatedBy: {
              id: log.updatedBy,
              name: '', // Not available in response
              role: ''  // Not available in response
            },
            remarks: log.remarks,
            updatedOn: log.updatedAt
          });
        });
      }

      // Map kycStatus
      const kycStatus = {
        businessProofVerified: !!documents.find(d => d.type === 'BusinessProof' && d.verified),
        gstVerified: !!documents.find(d => d.type === 'GST' && d.verified),
        addressVerified: !!documents.find(d => d.type === 'Aadhar' && d.verified),
        panVerified: !!documents.find(d => d.type === 'PAN' && d.verified),
        aadharVerified: !!documents.find(d => d.type === 'Aadhar' && d.verified),
        overallStatus: response.status || 'Pending'
      };

      // Create franchise object
      const franchiseData = {
        franchiseId: response._id,
        type: response.sk_franchise_details?.franchise_type || 'SF',
        subType: response.sk_franchise_details?.franchise_sub_type || 'SFSELLER',
        groupType: response.groupType || 'COCO',
        name: response.name,
        email: owner.email || '',
        mobile: String(owner.mobile || response.whatsAppNumber || response.username || ''),
        altMobile: '', // Not available in response
        gstNumber: finance.gstNo || '',
        sizeSqFt: undefined, // Not available in response
        ownershipType: undefined, // Not available in response
        rentAgreementUrl: undefined, // Not available in response
        addressLine1: address.door_no || '',
        addressLine2: address.street || '',
        city: response.town || '',
        district: response.district || '',
        state: response.state || '',
        pincode: String(response.pincode || ''),
        latitude,
        longitude,
        ownerDetails: {
          name: owner.name || '',
          aadharNumber: '', // Not available in response
          panNumber: finance.pan_no || '',
          idProofUrl: '', // Not available in response
          photoUrl: '', // Not available in response
        },
        documents,
        kycStatus,
        statusLogs,
        createdAt: response.createdAt,
        updatedAt: response.lastUpdated || response.updatedAt
      };
      await franchise.create(franchiseData).catch(err=>{
        reject(err)
      })
    }
  })
}



const saveOrUpdateUserToken=async (userId, token) =>{
  // Find if a token already exists for this user
  const existing = await userAuth.findOne({ userId });
  if (existing) {
    // Update the token
    existing.token = token;
    await existing.save();
    return existing;
  } else {
    // Create a new token record
    const newToken = new userAuth({ userId, token });
    await newToken.save();
    return newToken;
  }
}




module.exports = {
  loginUser
};
