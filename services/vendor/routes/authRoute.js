const express = require('express');
const router = express.Router();

// POST /auth/login - Vendor login
router.post('/login', function(req, res) {
  res.json({
    success: true,
    message: 'Vendor login endpoint',
    data: { email: req.body.email },
    timestamp: new Date().toISOString()
  });
});

// POST /auth/register - Vendor registration
router.post('/register', function(req, res) {
  res.json({
    success: true,
    message: 'Vendor registration endpoint',
    data: { email: req.body.email },
    timestamp: new Date().toISOString()
  });
});

// POST /auth/logout - Vendor logout
router.post('/logout', function(req, res) {
  res.json({
    success: true,
    message: 'Vendor logout endpoint',
    timestamp: new Date().toISOString()
  });
});

// GET /auth/profile - Get vendor profile
router.get('/profile', function(req, res) {
  res.json({
    success: true,
    message: 'Vendor profile endpoint',
    data: { user: 'vendor' },
    timestamp: new Date().toISOString()
  });
});

module.exports = router;