const Vendor = require('../models/vendor.model');
const { <PERSON>rror<PERSON>and<PERSON> } = require('../middleware/errorHandler');

/**
 * Vendor Service
 * Handles business logic for vendor management operations
 */

/**
 * Create a new vendor
 */
const createVendor = async (vendorData) => {
  try {
    // Check if vendor with same email already exists
    if (vendorData.email) {
      const existingVendor = await Vendor.findOne({ email: vendorData.email });
      if (existingVendor) {
        throw ErrorHandler.conflict('Vendor with this email already exists');
      }
    }

    // Create new vendor
    const vendor = new Vendor(vendorData);
    await vendor.save();

    return {
      success: true,
      message: 'Vendor created successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      // Handle duplicate key error
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Get all vendors with pagination and filtering
 */
const getVendors = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.type) {
      query.vendorType = filters.type;
    }
    if (filters.city) {
      query.city = new RegExp(filters.city, 'i');
    }
    if (filters.state) {
      query.state = new RegExp(filters.state, 'i');
    }
    if (filters.search) {
      query.$or = [
        { name: new RegExp(filters.search, 'i') },
        { vendorId: new RegExp(filters.search, 'i') },
        { email: new RegExp(filters.search, 'i') },
        { franchiseId: new RegExp(filters.search, 'i') },
        { franchiseName: new RegExp(filters.search, 'i') }
      ];
    }

    //dont include auditLog, createdBy, createdOn, modifiedBy, modifiedOn

    const vendors = await Vendor.find(query)
      .sort({ createdOn: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields

    const total = await Vendor.countDocuments(query);
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: 'Vendors retrieved successfully',
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendor by ID
 */
const getVendorById = async (vendorId) => {
  try {
    const vendor = await Vendor.findById(vendorId)
      .select('-aadharNo -aadharNoHashKey'); // Exclude sensitive fields

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor retrieved successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor by ID
 */
const updateVendor = async (vendorId, updateData, modifiedBy = 'SYSTEM') => {
  try {
    // Check if vendor exists
    const existingVendor = await Vendor.findById(vendorId);
    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Check for email conflicts if email is being updated and is different from existing email
    if (updateData.email && updateData.email !== existingVendor.email) {
      const emailExists = await Vendor.findOne({
        email: updateData.email,
        _id: { $ne: vendorId }
      });

      if (emailExists) {
        throw ErrorHandler.conflict('Vendor with this email already exists');
      }
    }

    // Update modification timestamp and user
    updateData.modifiedOn = new Date();
    updateData.modifiedBy = modifiedBy;

    const vendor = await Vendor.findByIdAndUpdate(
      vendorId,
      updateData,
      { new: true, runValidators: true }
    ).select('-aadharNo -aadharNoHashKey');

    return {
      success: true,
      message: 'Vendor updated successfully',
      vendor: vendor
    };
  } catch (error) {
    if (error.code === 11000) {
      const field = Object.keys(error.keyValue)[0];
      throw ErrorHandler.conflict(`Vendor with this ${field} already exists`);
    }
    throw error;
  }
};

/**
 * Delete vendor by ID
 */
const deleteVendor = async (vendorId) => {
  try {
    const vendor = await Vendor.findByIdAndDelete(vendorId);

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    return {
      success: true,
      message: 'Vendor deleted successfully',
      vendor: vendor
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendors by status
 */
const getVendorsByStatus = async (status, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const vendors = await Vendor.find({ status: status })
      .sort({ createdOn: -1 })
      .skip(skip)
      .limit(parseInt(limit))
      .select('-aadharNo -aadharNoHashKey');

    const total = await Vendor.countDocuments({ status: status });
    const totalPages = Math.ceil(total / limit);

    return {
      success: true,
      message: `${status} vendors retrieved successfully`,
      vendors: vendors,
      pagination: {
        currentPage: parseInt(page),
        totalPages: totalPages,
        totalItems: total,
        itemsPerPage: parseInt(limit),
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor status
 */
const updateVendorStatus = async (vendorId, status, userId, remarks = '') => {
  try {
    const vendor = await Vendor.findById(vendorId);

    if (!vendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Add to audit log
    const auditEntry = {
      status: status,
      createdOn: new Date(),
      createdBy: userId,
      remarks: remarks
    };

    const updatedVendor = await Vendor.findByIdAndUpdate(
      vendorId,
      {
        status: status,
        modifiedBy: userId,
        modifiedOn: new Date(),
        $push: { auditLog: auditEntry }
      },
      { new: true, runValidators: true }
    ).select('-aadharNo -aadharNoHashKey');

    return {
      success: true,
      message: 'Vendor status updated successfully',
      vendor: updatedVendor
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createVendor,
  getVendors,
  getVendorById,
  updateVendor,
  deleteVendor,
  getVendorsByStatus,
  updateVendorStatus
};