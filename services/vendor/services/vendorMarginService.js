const VendorMargin = require('../models/vendorMargin.model');
const Vendor = require('../models/vendor.model');
const { ErrorHandler } = require('../middleware/errorHandler');

/**
 * VendorMargin Service
 * Handles business logic for vendor margin management operations
 */

/**
 * Create a new vendor margin
 */
const createVendorMargin = async (marginData) => {
  try {
    // Check if vendor exists
    const existingVendor = await Vendor.findOne({ vendorId: marginData.vendorId });
    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    // Create new vendor margin
    const margin = new VendorMargin(marginData);
    await margin.save();

    return {
      success: true,
      message: 'Vendor margin created successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get vendor margin by ID (supports both MongoDB _id and custom vendorMarginId)
 */
const getVendorMarginById = async (id) => {
  try {
    let margin;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      margin = await VendorMargin.findById(id);
    } else {
      // Use custom vendorMarginId (like **********)
      margin = await VendorMargin.findOne({ vendorMarginId: id });
    }

    if (!margin) {
      throw ErrorHandler.notFound('Vendor margin not found');
    }

    return {
      success: true,
      message: 'Vendor margin retrieved successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get all vendor margins with pagination and filtering
 */
const getVendorMargins = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.vendorId) {
      query.vendorId = filters.vendorId;
    }
    if (filters.type) {
      query.type = filters.type;
    }
    if (filters.brandId) {
      query.brandId = filters.brandId;
    }
    if (filters.categoryId) {
      query.categoryId = filters.categoryId;
    }

    const margins = await VendorMargin.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await VendorMargin.countDocuments(query);

    return {
      success: true,
      margins: margins,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update vendor margin by ID (supports both MongoDB _id and custom vendorMarginId)
 */
const updateVendorMargin = async (id, updateData, modifiedBy = 'SYSTEM') => {
  try {
    let existingMargin;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      existingMargin = await VendorMargin.findById(id);
    } else {
      existingMargin = await VendorMargin.findOne({ vendorMarginId: id });
    }

    if (!existingMargin) {
      throw ErrorHandler.notFound('Vendor margin not found');
    }

    // Update modification timestamp and user
    updateData.modifiedOn = new Date();
    updateData.modifiedBy = modifiedBy;

    const margin = await VendorMargin.findByIdAndUpdate(
      existingMargin._id,
      updateData,
      { new: true, runValidators: true }
    );

    return {
      success: true,
      message: 'Vendor margin updated successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Delete vendor margin by ID (supports both MongoDB _id and custom vendorMarginId)
 */
const deleteVendorMargin = async (id) => {
  try {
    let margin;

    // Check if the ID looks like a MongoDB ObjectId (24 hex characters)
    if (id.match(/^[0-9a-fA-F]{24}$/)) {
      // Use MongoDB _id
      margin = await VendorMargin.findByIdAndDelete(id);
    } else {
      // Use custom vendorMarginId (like **********)
      margin = await VendorMargin.findOneAndDelete({ vendorMarginId: id });
    }

    if (!margin) {
      throw ErrorHandler.notFound('Vendor margin not found');
    }

    return {
      success: true,
      message: 'Vendor margin deleted successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get all margins for a specific vendor
 */
const getMarginsByVendorId = async (vendorId, page = 1, limit = 10) => {
  try {
    // Check if vendor exists
    const existingVendor = await Vendor.findOne({ vendorId: vendorId });
    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    const skip = (page - 1) * limit;
    const query = { vendorId: vendorId, isActive: true };

    const margins = await VendorMargin.find(query)
      .sort({ createdOn: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await VendorMargin.countDocuments(query);

    return {
      success: true,
      message: 'Vendor margins retrieved successfully',
      margins: margins,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get margin for a specific brand by vendor
 */
const getBrandMarginByVendor = async (vendorId, brandId) => {
  try {
    // Check if vendor exists
    const existingVendor = await Vendor.findOne({ vendorId: vendorId });
    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    const margin = await VendorMargin.findOne({
      vendorId: vendorId,
      type: 'Brand',
      brandId: brandId,
      isActive: true
    });

    if (!margin) {
      throw ErrorHandler.notFound(`No active margin found for vendor ${vendorId} and brand ${brandId}`);
    }

    return {
      success: true,
      message: 'Brand margin retrieved successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get margin for a specific category by vendor
 */
const getCategoryMarginByVendor = async (vendorId, categoryId) => {
  try {
    // Check if vendor exists
    const existingVendor = await Vendor.findOne({ vendorId: vendorId });
    if (!existingVendor) {
      throw ErrorHandler.notFound('Vendor not found');
    }

    const margin = await VendorMargin.findOne({
      vendorId: vendorId,
      type: 'Category',
      categoryId: categoryId,
      isActive: true
    });

    if (!margin) {
      throw ErrorHandler.notFound(`No active margin found for vendor ${vendorId} and category ${categoryId}`);
    }

    return {
      success: true,
      message: 'Category margin retrieved successfully',
      margin: margin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update margin percentage for a specific vendor margin
 */
const updateMarginPercentage = async (vendorId, marginId, newMargin, modifiedBy) => {
  try {
    const existingMargin = await VendorMargin.findOne({
      _id: marginId,
      vendorId: vendorId,
      isActive: true
    });

    if (!existingMargin) {
      throw ErrorHandler.notFound('Vendor margin not found');
    }

    // Add audit log entry
    const auditEntry = {
      action: 'MARGIN_UPDATED',
      modifiedBy: modifiedBy || 'SYSTEM',
      modifiedOn: new Date(),
      remarks: `Margin updated from ${existingMargin.margin}% to ${newMargin}%`
    };

    const updatedMargin = await VendorMargin.findByIdAndUpdate(
      marginId,
      {
        margin: newMargin,
        modifiedBy: modifiedBy || 'SYSTEM',
        modifiedOn: new Date(),
        $push: { auditLog: auditEntry }
      },
      { new: true, runValidators: true }
    );

    return {
      success: true,
      message: 'Margin percentage updated successfully',
      margin: updatedMargin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update brandId for a brand margin
 */
const updateBrandMargin = async (vendorId, marginId, newBrandId, modifiedBy) => {
  try {
    const existingMargin = await VendorMargin.findOne({
      _id: marginId,
      vendorId: vendorId,
      type: 'Brand',
      isActive: true
    });

    if (!existingMargin) {
      throw ErrorHandler.notFound('Brand margin not found');
    }

    // Check if vendor already has margin for the new brandId
    const duplicateCheck = await VendorMargin.findOne({
      vendorId: vendorId,
      type: 'Brand',
      brandId: newBrandId,
      isActive: true,
      _id: { $ne: marginId }
    });

    if (duplicateCheck) {
      throw ErrorHandler.badRequest(`Vendor ${vendorId} already has an active margin for brand ${newBrandId}`);
    }

    // Add audit log entry
    const auditEntry = {
      action: 'BRAND_UPDATED',
      modifiedBy: modifiedBy || 'SYSTEM',
      modifiedOn: new Date(),
      remarks: `Brand updated from ${existingMargin.brandId} to ${newBrandId}`
    };

    const updatedMargin = await VendorMargin.findByIdAndUpdate(
      marginId,
      {
        brandId: newBrandId,
        modifiedBy: modifiedBy || 'SYSTEM',
        modifiedOn: new Date(),
        $push: { auditLog: auditEntry }
      },
      { new: true, runValidators: true }
    );

    return {
      success: true,
      message: 'Brand margin updated successfully',
      margin: updatedMargin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update categoryId for a category margin
 */
const updateCategoryMargin = async (vendorId, marginId, newCategoryId, modifiedBy) => {
  try {
    const existingMargin = await VendorMargin.findOne({
      _id: marginId,
      vendorId: vendorId,
      type: 'Category',
      isActive: true
    });

    if (!existingMargin) {
      throw ErrorHandler.notFound('Category margin not found');
    }

    // Check if vendor already has margin for the new categoryId
    const duplicateCheck = await VendorMargin.findOne({
      vendorId: vendorId,
      type: 'Category',
      categoryId: newCategoryId,
      isActive: true,
      _id: { $ne: marginId }
    });

    if (duplicateCheck) {
      throw ErrorHandler.badRequest(`Vendor ${vendorId} already has an active margin for category ${newCategoryId}`);
    }

    // Add audit log entry
    const auditEntry = {
      action: 'CATEGORY_UPDATED',
      modifiedBy: modifiedBy || 'SYSTEM',
      modifiedOn: new Date(),
      remarks: `Category updated from ${existingMargin.categoryId} to ${newCategoryId}`
    };

    const updatedMargin = await VendorMargin.findByIdAndUpdate(
      marginId,
      {
        categoryId: newCategoryId,
        modifiedBy: modifiedBy || 'SYSTEM',
        modifiedOn: new Date(),
        $push: { auditLog: auditEntry }
      },
      { new: true, runValidators: true }
    );

    return {
      success: true,
      message: 'Category margin updated successfully',
      margin: updatedMargin
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Add new brand margin for a vendor
 */
const addBrandMargin = async (vendorId, brandId, margin, startDate, endDate, createdBy) => {
  try {
    const marginData = {
      vendorId: vendorId,
      type: 'Brand',
      brandId: brandId,
      margin: margin,
      startDate: startDate,
      endDate: endDate,
      createdBy: createdBy || 'SYSTEM',
      sourceAllBrands: false,
      isActive: true
    };

    return await createVendorMargin(marginData);
  } catch (error) {
    throw error;
  }
};

/**
 * Add new category margin for a vendor
 */
const addCategoryMargin = async (vendorId, categoryId, margin, startDate, endDate, createdBy) => {
  try {
    const marginData = {
      vendorId: vendorId,
      type: 'Category',
      categoryId: categoryId,
      margin: margin,
      startDate: startDate,
      endDate: endDate,
      createdBy: createdBy || 'SYSTEM',
      sourceAllBrands: false,
      isActive: true
    };

    return await createVendorMargin(marginData);
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createVendorMargin,
  getVendorMarginById,
  getVendorMargins,
  updateVendorMargin,
  deleteVendorMargin,
  getMarginsByVendorId,
  getBrandMarginByVendor,
  getCategoryMarginByVendor,
  updateMarginPercentage,
  updateBrandMargin,
  updateCategoryMargin,
  addBrandMargin,
  addCategoryMargin
};