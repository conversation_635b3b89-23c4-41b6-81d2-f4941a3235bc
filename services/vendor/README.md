# Vendor Management Service

A comprehensive vendor management microservice built with Node.js, Express, and MongoDB. This service provides complete vendor lifecycle management with JWT authentication, role-based access control, and RESTful APIs.

## 🚀 Features

### Core Functionality
- **Vendor Management**: Complete CRUD operations for vendor accounts
- **Authentication**: JWT-based authentication with session management
- **Authorization**: Role-based access control (vendor, admin)
- **Vendor Profiles**: Comprehensive vendor profiles
- **Search & Filtering**: Advanced search and filtering capabilities
- **Pagination**: Efficient pagination for large datasets

### Security Features
- **JWT Authentication**: Secure token-based authentication
- **Session Management**: Track and manage vendor sessions
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive data validation using Joi
- **Security Headers**: CORS, Helmet, and other security middleware
- **Password Security**: Bcrypt hashing with configurable salt rounds

### Monitoring & Observability
- **Health Checks**: Comprehensive health monitoring endpoints
- **Request Logging**: Detailed request/response logging
- **Performance Monitoring**: Track slow requests and performance metrics
- **Error Handling**: Centralized error handling with proper logging
- **Metrics**: Business and technical metrics endpoints

### Database Features
- **MongoDB Integration**: Mongoose ODM with advanced schema design
- **Transactions**: Database transactions for data consistency
- **Indexing**: Optimized database indexes for performance
- **Connection Pooling**: Efficient database connection management

## 📋 Prerequisites

- Node.js 18+ (recommended: 22.x LTS)
- MongoDB 4.4+
- npm or yarn

## 🛠️ Installation

1. **Navigate to the vendor service directory**
   ```bash
   cd services/vendor
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the service**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=5002
NODE_ENV=development

# Database Configuration
MONGODB_URI=your-mongodb-connection-string

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Service Configuration
SERVICE_NAME=vendor-service
SERVICE_VERSION=1.0.0
```

## 📚 API Documentation

### Authentication Endpoints

**Via API Gateway:**
- `POST /api/vendor/auth/login` - Vendor login
- `POST /api/vendor/auth/logout` - Vendor logout
- `GET /api/vendor/auth/profile` - Get current vendor profile
- `POST /api/vendor/auth/register` - Vendor registration

**Direct Service Access:**
- `POST /auth/login` - Vendor login
- `POST /auth/logout` - Vendor logout
- `GET /auth/profile` - Get current vendor profile
- `POST /auth/register` - Vendor registration

### Vendor Management Endpoints

**Via API Gateway:**
- `POST /api/vendor/` - Create new vendor (Admin only)
- `GET /api/vendor/` - Get vendors with pagination and filtering
- `GET /api/vendor/:id` - Get vendor by ID
- `PUT /api/vendor/:id` - Update vendor
- `DELETE /api/vendor/:id` - Delete vendor (Admin only)
- `GET /api/vendor/status/:status` - Get vendors by status
- `PATCH /api/vendor/:id/status` - Update vendor status

**Direct Service Access:**
- `POST /vendor/` - Create new vendor (Admin only)
- `GET /vendor/` - Get vendors with pagination and filtering
- `GET /vendor/:id` - Get vendor by ID
- `PUT /vendor/:id` - Update vendor
- `DELETE /vendor/:id` - Delete vendor (Admin only)
- `GET /vendor/status/:status` - Get vendors by status
- `PATCH /vendor/:id/status` - Update vendor status

### Vendor Margin Management Endpoints

#### Basic Margin Operations

**Via API Gateway:**
- `POST /api/vendor/margin` - Create new vendor margin
- `GET /api/vendor/margin` - Get all vendor margins with pagination and filtering
- `GET /api/vendor/margin/:id` - Get vendor margin by ID
- `PUT /api/vendor/margin/:id` - Update vendor margin
- `DELETE /api/vendor/margin/:id` - Delete vendor margin

**Direct Service Access:**
- `POST /vendor/margin` - Create new vendor margin
- `GET /vendor/margin` - Get all vendor margins with pagination and filtering
- `GET /vendor/margin/:id` - Get vendor margin by ID
- `PUT /vendor/margin/:id` - Update vendor margin
- `DELETE /vendor/margin/:id` - Delete vendor margin

#### Advanced Margin Management

**Via API Gateway:**
- `GET /api/vendor/margin/vendor/:vendorId` - Get all margins for a specific vendor
- `GET /api/vendor/margin/vendor/:vendorId/brand/:brandId` - Get margin for a specific brand by vendor
- `GET /api/vendor/margin/vendor/:vendorId/category/:categoryId` - Get margin for a specific category by vendor
- `PUT /api/vendor/margin/vendor/:vendorId/margin/:marginId/percentage` - Update margin percentage
- `PUT /api/vendor/margin/vendor/:vendorId/margin/:marginId/brand` - Update brandId for a brand margin
- `PUT /api/vendor/margin/vendor/:vendorId/margin/:marginId/category` - Update categoryId for a category margin
- `POST /api/vendor/margin/vendor/:vendorId/brand` - Add new brand margin for a vendor
- `POST /api/vendor/margin/vendor/:vendorId/category` - Add new category margin for a vendor

**Direct Service Access:**
- `GET /vendor/margin/vendor/:vendorId` - Get all margins for a specific vendor
- `GET /vendor/margin/vendor/:vendorId/brand/:brandId` - Get margin for a specific brand by vendor
- `GET /vendor/margin/vendor/:vendorId/category/:categoryId` - Get margin for a specific category by vendor
- `PUT /vendor/margin/vendor/:vendorId/margin/:marginId/percentage` - Update margin percentage
- `PUT /vendor/margin/vendor/:vendorId/margin/:marginId/brand` - Update brandId for a brand margin
- `PUT /vendor/margin/vendor/:vendorId/margin/:marginId/category` - Update categoryId for a category margin
- `POST /vendor/margin/vendor/:vendorId/brand` - Add new brand margin for a vendor
- `POST /vendor/margin/vendor/:vendorId/category` - Add new category margin for a vendor

### Health & Monitoring

- `GET /health` - Basic health check
- `GET /health/detailed` - Detailed health check
- `GET /health/ready` - Readiness probe
- `GET /health/live` - Liveness probe
- `GET /metrics` - Service metrics

## 🗄️ Database Schema

### Vendors Collection

```javascript
{
  _id: "VE[22-char-uuid]",           // Primary key with VE prefix
  type: "vendor|admin",
  vendorId: "ObjectId",                // Reference/foreign key
  name: "String",                    // Vendor name
  contactNo: "String",               // 10-digit contact number
  email: "String",                   // Unique email address
  address: {
    doorNo: "String",                // Door/house number
    street: "String",                // Street name
    city: "String",                  // City name
    state: "String",                 // State/province
    postcode: "String"               // 6-digit postal code
  },
  registeredOn: "Date",              // Registration timestamp
  lastLogin: "Date",                 // Last login timestamp
  status: "Active|Inactive|Suspended",
  createdBy: "ObjectId",             // Reference to creator
  createdOn: "Date",                 // Creation timestamp
  modifiedBy: "ObjectId",            // Reference to modifier
  modifiedOn: "Date"                 // Last modification timestamp
}
```

### VendorAuth Collection

```javascript
{
  _id: "ObjectId",                   // Primary key
  vendorId: "ObjectId",              // Foreign key to Vendors._id
  loginFrom: "String",               // Login source/device
  token: "String",                   // Authentication token
  expiresOn: "Date",                 // Token expiration
  ipAddress: "String",               // IP address of login
  loggedOn: "Date"                   // Login timestamp
}
```

## 🔒 Security

- JWT tokens with configurable expiration
- Rate limiting on authentication endpoints
- Input validation and sanitization
- Security headers (CORS, Helmet, etc.)
- Password hashing with bcrypt
- Session management and tracking

## 🧪 Testing 