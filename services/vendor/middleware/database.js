const mongoose = require('mongoose');
require('dotenv').config({ path: require('path').join(__dirname, '..', '.env') });

let isConnected = false;
const connectionString = process.env.MONGODB_URI;

const connect = function() {
  return new Promise(function(resolve, reject) {
    if (isConnected) {
      console.log('Database already connected');
      return resolve();
    }

    const options = {
      maxPoolSize: 10,
      connectTimeoutMS: 30000,
      socketTimeoutMS: 30000,
      serverSelectionTimeoutMS: 5000,
      heartbeatFrequencyMS: 10000,
      maxIdleTimeMS: 30000
    };

    mongoose.connect(connectionString, options)
      .then(function() {
        isConnected = true;
        console.log('✅ Successfully connected to MongoDB');

        // Handle connection events
        mongoose.connection.on('error', function(err) {
          console.error('❌ MongoDB connection error:', err);
          isConnected = false;
        });

        mongoose.connection.on('disconnected', function() {
          console.log('⚠️ MongoDB disconnected');
          isConnected = false;
        });

        mongoose.connection.on('reconnected', function() {
          console.log('✅ MongoDB reconnected');
          isConnected = true;
        });

        resolve();
      })
      .catch(function(error) {
        console.error('❌ Unable to connect to MongoDB:', error);

        // Check for MongoDB version compatibility issues
        if (error.message && error.message.includes('wire version')) {
          console.error('💡 MongoDB version compatibility issue detected.');
          console.error('💡 Mongoose 8.x requires MongoDB 4.2+ (wire version 8).');
          console.error('💡 Current MongoDB server appears to be version 3.x (wire version 5).');
          console.error('💡 Please upgrade MongoDB or use a compatible Mongoose version.');
        }

        isConnected = false;
        reject(error);
      });
  });
};

const disconnect = function() {
  return new Promise(function(resolve, reject) {
    mongoose.connection.close()
      .then(function() {
        isConnected = false;
        console.log('✅ Disconnected from MongoDB');
        resolve();
      })
      .catch(function(error) {
        console.error('❌ Error disconnecting from MongoDB:', error);
        reject(error);
      });
  });
};

const getConnectionStatus = function() {
  return {
    isConnected: isConnected,
    readyState: mongoose.connection.readyState,
    host: mongoose.connection.host,
    name: mongoose.connection.name
  };
};

// Legacy function for backward compatibility
const connectDb = function() {
  return connect().catch(function(error) {
    console.error("Unable to connect MongoDB", error);
    process.exit(1);
  });
};

module.exports = {
  connect: connect,
  disconnect: disconnect,
  getConnectionStatus: getConnectionStatus,
  connectDb: connectDb // Keep for backward compatibility
};