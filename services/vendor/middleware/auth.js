const jwt = require('jsonwebtoken');
const Vendor = require('../models/Vendor');
const VendorAuth = require('../models/VendorAuth');

// JWT Configuration with fallback
const jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
const jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';

/**
 * Authentication Middleware for Vendor Service
 * Provides JWT token validation and error handling
 */

/**
 * Generate JWT token
 */
const generateToken = (payload, expiredIn) => {
  return jwt.sign(payload, jwtSecret, { expiresIn: expiredIn || jwtExpiresIn });
};

/**
 * Generate password reset token
 */
const generatePasswordResetToken = (payload) => {
  return jwt.sign(payload, jwtSecret, { expiresIn: '1h' }); // 1 hour expiry for reset tokens
};

/**
 * Verify JWT token
 */
const verifyToken = (token) => {
  console.log('Verifying token:', jwtSecret);
  try {
    return jwt.verify(token, jwtSecret);
  } catch (error) {
    throw new Error('Invalid token');
  }
};

/**
 * Extract token from request headers
 */
const extractToken = (req) => {
  const authHeader = req.headers.authorization;
  if (!authHeader) return null;

  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return authHeader;
};

/**
 * Extract vendor context from API Gateway headers
 */
const extractVendorFromHeaders = (req) => {
  const vendorId = req.headers['x-vendor-id'];
  const vendorType = req.headers['x-vendor-type'];
  const vendorName = req.headers['x-vendor-name'];
  const vendorEmail = req.headers['x-vendor-email'];
  const vendorStatus = req.headers['x-vendor-status'];

  if (!vendorId) {
    return null;
  }

  return {
    id: vendorId,
    type: vendorType || 'Individual', // Default type
    name: vendorName || 'Unknown Vendor',
    email: vendorEmail || '',
    status: vendorStatus || 'Active' // Default status
  };
};

/**
 * Authenticate vendor with JWT token or API Gateway context
 * When called from API Gateway, vendor context is passed via headers
 * When called directly, falls back to JWT token validation
 */
const authenticate = () => {
  return async (req, res, next) => {
    try {
      // First, check if vendor context is provided by API Gateway
      const gatewayVendor = extractVendorFromHeaders(req);

      if (gatewayVendor) {
        console.log(`[${req.requestId}] Using API Gateway vendor context for vendor: ${gatewayVendor.id}`);

        // Vendor authenticated by API Gateway, use the provided context directly
        // This avoids database lookup since API Gateway already validated the vendor
        req.vendor = {
          id: gatewayVendor.id,
          vendorId: gatewayVendor.id, // Use same ID for vendorId
          name: gatewayVendor.name,
          email: gatewayVendor.email,
          type: gatewayVendor.type,
          status: gatewayVendor.status
        };

        return next();
      }

      console.log(`[${req.requestId}] No API Gateway context, falling back to JWT token validation`);

      // Fallback to JWT token validation for direct service calls
      const token = extractToken(req);

      if (!token) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          message: 'No token provided',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }

      // Verify token
      const decoded = verifyToken(token);
      console.log('Decoded token:', JSON.stringify(decoded, null, 2));

      // Check if token exists in VendorAuth and is not expired
      // Skip VendorAuth check for internal service calls (when User-Agent contains service name)
      const userAgent = req.headers['user-agent'] || '';
      const isInternalCall = userAgent.includes('axios') || userAgent.includes('node');

      if (!isInternalCall) {
        const vendorAuth = await VendorAuth.findValidToken(token);
        if (!vendorAuth) {
          return res.status(401).json({
            success: false,
            error: 'Invalid token',
            message: 'Token not found or expired',
            requestId: req.requestId,
            timestamp: new Date().toISOString()
          });
        }
      }

      // Get vendor details
      console.log('Looking for vendor with ID:', decoded.id);
      const vendor = await Vendor.findById(decoded.id);
      console.log('Vendor found:', vendor ? 'YES' : 'NO');
      if (vendor) {
        console.log('Vendor status:', vendor.status);
      }

      if (!vendor || vendor.status !== 'Active') {
        console.log('Vendor lookup failed - vendor:', !!vendor, 'status:', vendor?.status);
        return res.status(401).json({
          success: false,
          error: 'Vendor not found or inactive',
          requestId: req.requestId,
          timestamp: new Date().toISOString()
        });
      }

      // Attach vendor and token info to request
      req.vendor = {
        id: vendor._id,
        vendorId: vendor.refId,
        name: vendor.name,
        email: vendor.email,
        type: vendor.type,
        status: vendor.status
      };
      req.token = token;
      if (!isInternalCall) {
        const vendorAuth = await VendorAuth.findValidToken(token);
        req.vendorAuth = vendorAuth;
      }

      next();
    } catch (error) {
      console.error(`[${req.requestId}] Authentication error:`, error);

      return res.status(401).json({
        success: false,
        error: 'Authentication failed',
        message: error.message,
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }
  };
};

/**
 * Optional authentication - doesn't fail if no token provided
 * Supports both API Gateway context and direct JWT token validation
 */
const optionalAuthenticate = () => {
  return async (req, _res, next) => {
    try {
      // First, check if vendor context is provided by API Gateway
      const gatewayVendor = extractVendorFromHeaders(req);

      if (gatewayVendor) {
        console.log(`[${req.requestId}] Using API Gateway vendor context for optional auth: ${gatewayVendor.id}`);

        // Vendor authenticated by API Gateway, use the provided context directly
        req.vendor = {
          id: gatewayVendor.id,
          vendorId: gatewayVendor.id, // Use same ID for vendorId
          name: gatewayVendor.name,
          email: gatewayVendor.email,
          type: gatewayVendor.type,
          status: gatewayVendor.status
        };

        return next();
      }

      // Fallback to JWT token validation
      const token = extractToken(req);

      if (!token) {
        return next();
      }

      const decoded = verifyToken(token);
      const vendorAuth = await VendorAuth.findValidToken(token);

      if (vendorAuth) {
        const vendor = await Vendor.findById(decoded.id);
        if (vendor && vendor.status === 'Active') {
          req.vendor = {
            id: vendor._id,
            vendorId: vendor.refId,
            name: vendor.name,
            email: vendor.email,
            type: vendor.type,
            status: vendor.status
          };
          req.token = token;
          req.vendorAuth = vendorAuth;
        }
      }

      next();
    } catch (error) {
      // For optional auth, we don't fail on errors
      console.warn(`[${req.requestId}] Optional authentication warning:`, error.message);
      next();
    }
  };
};

/**
 * Require specific vendor type
 */
const requireType = (allowedTypes) => {
  return (req, res, next) => {
    if (!req.vendor) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    const vendorTypes = Array.isArray(allowedTypes) ? allowedTypes : [allowedTypes];

    if (!vendorTypes.includes(req.vendor.type)) {
      return res.status(403).json({
        success: false,
        error: 'Access denied',
        message: `Required vendor type: ${vendorTypes.join(' or ')}`,
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    next();
  };
};

/**
 * Require admin access
 */
const requireAdmin = () => {
  return requireType('admin');
};

/**
 * Require company or corporation vendor access
 */
const requireCompanyOrCorporation = () => {
  return requireType(['Company', 'Corporation']);
};

/**
 * Check if vendor can access specific vendor data
 */
const canAccessVendor = () => {
  return (req, res, next) => {
    if (!req.vendor) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    const targetVendorId = req.params.id || req.params.vendorId;

    // Admin can access all vendor data
    if (req.vendor.type === 'admin') {
      return next();
    }

    // Vendors can only access their own data
    if (req.vendor.id === targetVendorId || req.vendor.vendorId === targetVendorId) {
      return next();
    }

    return res.status(403).json({
      success: false,
      error: 'Access denied',
      message: 'You can only access your own vendor data',
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  };
};

/**
 * Rate limiting for authentication endpoints
 */
const authRateLimit = () => {
  const attempts = new Map();
  const maxAttempts = 5;
  const windowMs = 15 * 60 * 1000; // 15 minutes

  return (req, res, next) => {
    const key = req.ip + ':' + (req.body.email || req.body.mobileNo || 'unknown');
    const now = Date.now();

    if (!attempts.has(key)) {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    const attempt = attempts.get(key);

    if (now > attempt.resetTime) {
      attempts.set(key, { count: 1, resetTime: now + windowMs });
      return next();
    }

    if (attempt.count >= maxAttempts) {
      return res.status(429).json({
        success: false,
        error: 'Too many authentication attempts',
        message: 'Please try again later',
        retryAfter: Math.ceil((attempt.resetTime - now) / 1000),
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });
    }

    attempt.count++;
    next();
  };
};

module.exports = {
  generateToken,
  generatePasswordResetToken,
  verifyToken,
  authenticate,
  optionalAuthenticate,
  requireType,
  requireAdmin,
  requireCompanyOrCorporation,
  canAccessVendor,
  authRateLimit
};