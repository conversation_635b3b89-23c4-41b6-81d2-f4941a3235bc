const mongoose = require('mongoose');
const { getConnectionStatus } = require('./database');

/**
 * Health Check Middleware (Node.js v6 compatible)
 * Provides comprehensive health monitoring for the vendor service
 */

/**
 * Basic health check endpoint
 */
const healthCheck = function(req, res) {
  const dbStatus = getConnectionStatus();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();

  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: {
      name: 'vendor-service',
      version: process.env.SERVICE_VERSION || '1.0.0',
      uptime: Math.floor(uptime / 60) + ' minutes',
      environment: process.env.NODE_ENV || 'development'
    },
    database: {
      status: dbStatus.isConnected ? 'connected' : 'disconnected',
      readyState: dbStatus.readyState,
      host: dbStatus.host,
      name: dbStatus.name
    },
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
      external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB'
    }
  };

  // Check if database is connected
  if (!dbStatus.isConnected) {
    health.status = 'unhealthy';
    return res.status(503).json(health);
  }

  res.status(200).json(health);
};

/**
 * Detailed health check with database connectivity test
 */
const detailedHealthCheck = function(req, res) {
  const dbStatus = getConnectionStatus();
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();

  const health = {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    service: {
      name: 'vendor-service',
      version: process.env.SERVICE_VERSION || '1.0.0',
      uptime: Math.floor(uptime / 60) + ' minutes',
      environment: process.env.NODE_ENV || 'development',
      nodeVersion: process.version,
      platform: process.platform,
      arch: process.arch
    },
    database: {
      status: dbStatus.isConnected ? 'connected' : 'disconnected',
      readyState: dbStatus.readyState,
      host: dbStatus.host,
      name: dbStatus.name
    },
    memory: {
      rss: Math.round(memoryUsage.rss / 1024 / 1024) + ' MB',
      heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024) + ' MB',
      heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024) + ' MB',
      external: Math.round(memoryUsage.external / 1024 / 1024) + ' MB'
    }
  };

  // Check if database is connected
  if (!dbStatus.isConnected) {
    health.status = 'unhealthy';
    return res.status(503).json(health);
  }

  res.status(200).json(health);
};

/**
 * Readiness probe for Kubernetes
 */
const readinessProbe = function(req, res) {
  const dbStatus = getConnectionStatus();

  if (dbStatus.isConnected) {
    res.status(200).json({ status: 'ready' });
  } else {
    res.status(503).json({ status: 'not ready' });
  }
};

/**
 * Liveness probe for Kubernetes
 */
const livenessProbe = function(req, res) {
  res.status(200).json({ status: 'alive' });
};

/**
 * Basic metrics endpoint
 */
const metrics = function(req, res) {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();

  res.status(200).json({
    uptime: uptime,
    memory: memoryUsage,
    timestamp: new Date().toISOString()
  });
};

module.exports = {
  healthCheck: healthCheck,
  detailedHealthCheck: detailedHealthCheck,
  readinessProbe: readinessProbe,
  livenessProbe: livenessProbe,
  metrics: metrics
};