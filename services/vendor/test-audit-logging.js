/**
 * Test script to demonstrate audit logging functionality
 * This script creates test data and performs updates to show audit logging in action
 */

const mongoose = require('mongoose');
const Vendor = require('./models/vendor.model');
const VendorMargin = require('./models/vendorMargin.model');

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/vendor-service', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Test vendor audit logging
const testVendorAuditLogging = async () => {
  console.log('\n🧪 Testing Vendor Audit Logging...');
  
  try {
    // Create a test vendor
    const vendor = new Vendor({
      vendorType: 'vendor',
      name: 'Test Vendor for Audit',
      contactNo: '9876543210',
      email: '<EMAIL>',
      address: {
        doorNo: '123',
        street: 'Test Street',
        city: 'Test City',
        state: 'Test State',
        postcode: '123456'
      },
      status: 'Active',
      createdBy: 'TEST_USER'
    });
    
    await vendor.save();
    console.log('✅ Created test vendor:', vendor.vendorId);
    
    // Update vendor name
    vendor.name = 'Updated Vendor Name';
    vendor.modifiedBy = 'TEST_USER_1';
    await vendor.save();
    console.log('✅ Updated vendor name');
    
    // Update multiple fields
    vendor.name = 'Final Vendor Name';
    vendor.contactNo = '9999999999';
    vendor.status = 'Inactive';
    vendor.modifiedBy = 'TEST_USER_2';
    await vendor.save();
    console.log('✅ Updated multiple vendor fields');
    
    // Update address (nested object)
    vendor.address.city = 'New City';
    vendor.address.state = 'New State';
    vendor.modifiedBy = 'TEST_USER_3';
    await vendor.save();
    console.log('✅ Updated vendor address');
    
    // Display audit log
    console.log('\n📋 Vendor Audit Log:');
    vendor.auditLog.forEach((entry, index) => {
      console.log(`${index + 1}. ${entry.status} by ${entry.createdBy} on ${entry.createdOn}`);
      console.log(`   Remarks: ${entry.remarks}`);
    });
    
    return vendor._id;
  } catch (error) {
    console.error('❌ Error testing vendor audit logging:', error);
  }
};

// Test vendor margin audit logging
const testVendorMarginAuditLogging = async (vendorId) => {
  console.log('\n🧪 Testing Vendor Margin Audit Logging...');
  
  try {
    // Create a test vendor margin
    const vendorMargin = new VendorMargin({
      vendorId: vendorId,
      type: 'Brand',
      brandId: 'BR001',
      margin: 10.5,
      isActive: true,
      createdBy: 'TEST_USER'
    });
    
    await vendorMargin.save();
    console.log('✅ Created test vendor margin:', vendorMargin.vendorMarginId);
    
    // Update margin percentage
    vendorMargin.margin = 15.0;
    vendorMargin.modifiedBy = 'TEST_USER_1';
    await vendorMargin.save();
    console.log('✅ Updated margin percentage');
    
    // Update brand ID
    vendorMargin.brandId = 'BR002';
    vendorMargin.modifiedBy = 'TEST_USER_2';
    await vendorMargin.save();
    console.log('✅ Updated brand ID');
    
    // Update multiple fields
    vendorMargin.margin = 20.0;
    vendorMargin.brandId = 'BR003';
    vendorMargin.isActive = false;
    vendorMargin.modifiedBy = 'TEST_USER_3';
    await vendorMargin.save();
    console.log('✅ Updated multiple margin fields');
    
    // Display audit log
    console.log('\n📋 Vendor Margin Audit Log:');
    vendorMargin.auditLog.forEach((entry, index) => {
      console.log(`${index + 1}. ${entry.action} by ${entry.modifiedBy} on ${entry.modifiedOn}`);
      console.log(`   Remarks: ${entry.remarks}`);
    });
    
    return vendorMargin._id;
  } catch (error) {
    console.error('❌ Error testing vendor margin audit logging:', error);
  }
};

// Cleanup test data
const cleanup = async (vendorId, marginId) => {
  console.log('\n🧹 Cleaning up test data...');
  
  try {
    if (marginId) {
      await VendorMargin.findByIdAndDelete(marginId);
      console.log('✅ Deleted test vendor margin');
    }
    
    if (vendorId) {
      await Vendor.findByIdAndDelete(vendorId);
      console.log('✅ Deleted test vendor');
    }
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  }
};

// Main test function
const runAuditLoggingTests = async () => {
  console.log('🚀 Starting Audit Logging Tests...');
  
  await connectDB();
  
  let vendorId, marginId;
  
  try {
    vendorId = await testVendorAuditLogging();
    marginId = await testVendorMarginAuditLogging(vendorId);
    
    console.log('\n✅ All audit logging tests completed successfully!');
    console.log('\n📝 Summary:');
    console.log('- Vendor audit logging tracks field changes with detailed remarks');
    console.log('- Vendor margin audit logging tracks field changes with detailed remarks');
    console.log('- Audit logs include who made the change and when');
    console.log('- Multiple field changes are handled appropriately');
    console.log('- Nested object changes (like address) are tracked');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    await cleanup(vendorId, marginId);
    await mongoose.connection.close();
    console.log('\n👋 Disconnected from MongoDB');
  }
};

// Run tests if this file is executed directly
if (require.main === module) {
  runAuditLoggingTests();
}

module.exports = {
  runAuditLoggingTests,
  testVendorAuditLogging,
  testVendorMarginAuditLogging
};
