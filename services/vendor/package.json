{"name": "vendor-service", "version": "1.0.0", "description": "Vendor Management Microservice", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "pm2:start": "pm2 start ../../ecosystem.config.js --only vendor", "pm2:stop": "pm2 stop vendor", "pm2:restart": "pm2 restart vendor", "pm2:reload": "pm2 reload vendor", "pm2:delete": "pm2 delete vendor", "pm2:logs": "pm2 logs vendor", "pm2:monit": "pm2 monit"}, "keywords": ["vendor", "microservice", "api", "management"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.1", "morgan": "^1.10.0", "serverless-http": "^3.2.0", "uuid": "^9.0.1"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}}