const Joi = require('joi');

/**
 * Vendor Validation Schema
 * Implements comprehensive validation for vendor data
 */

/**
 * Validate vendor creation data
 */

const validateVendorMarginCreation = (data) => {
  const schema = Joi.object({
    vendorId: Joi.string()
      .trim()
      .required()
      .messages({
        'string.empty': 'Vendor ID is required'
      }),
    type: Joi.string()
      .valid('Brand', 'Category')
      .required()
      .messages({
        'string.empty': 'Type is required',
        'any.only': 'Type must be one of: Brand, Category'
      }),
    brandId: Joi.string()
      .trim()
      .when('type', {
        is: 'Brand',
        then: Joi.required(),
        otherwise: Joi.forbidden()
      })
      .messages({
        'string.empty': 'Brand ID is required when type is Brand'
      }),
    categoryId: Joi.string()
      .trim()
      .when('type', {
        is: 'Category',
        then: Joi.required(),
        otherwise: Joi.forbidden()
      })
      .messages({
        'string.empty': 'Category ID is required when type is Category'
      }),
    margin: Joi.number()
      .min(0)
      .max(100)
      .required()
      .messages({
        'number.min': 'Margin cannot be less than 0',
        'number.max': 'Margin cannot be greater than 100',
        'number.base': 'Margin must be a number'
      }),
    startDate: Joi.date()
      .required()
      .messages({
        'date.base': 'Start date must be a valid date'
      }),
    endDate: Joi.date()
      .min(Joi.ref('startDate'))
      .messages({
        'date.base': 'End date must be a valid date',
        'date.min': 'End date must be after start date'
      }),
    sourceAllBrands: Joi.boolean()
      .required()
      .messages({
        'boolean.base': 'Source all brands must be a boolean'
      }),
    createdBy: Joi.string()
      .optional()
      .messages({
        'string.empty': 'Created by must be a valid string'
      })
  });

  return schema.validate(data, { abortEarly: false });
};

const validateVendorMarginUpdate = (data) => {
  const schema = Joi.object({
    margin: Joi.number()
      .min(0)
      .max(100)
      .optional()
      .messages({
        'number.min': 'Margin cannot be less than 0',
        'number.max': 'Margin cannot be greater than 100',
        'number.base': 'Margin must be a number'
      }),
    startDate: Joi.date()
      .optional()
      .messages({
        'date.base': 'Start date must be a valid date'
      }),
    endDate: Joi.date()
      .min(Joi.ref('startDate'))
      .optional()
      .messages({
        'date.base': 'End date must be a valid date',
        'date.min': 'End date must be after start date'
      }),
    sourceAllBrands: Joi.boolean()
      .optional()
      .messages({
        'boolean.base': 'Source all brands must be a boolean'
      })
  });

  return schema.validate(data, { abortEarly: false });
};

const validateVendorMarginCreationMiddleware = (req, res, next) => {
  const { error, value } = validateVendorMarginCreation(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};

const validateVendorMarginUpdateMiddleware = (req, res, next) => {
  const { error, value } = validateVendorMarginUpdate(req.body);

  if (error) {
    const details = error.details.map(detail => ({
      field: detail.path.join('.'),
      message: detail.message
    }));

    return res.status(400).json({
      success: false,
      error: 'Validation Error',
      message: 'Invalid input data',
      details,
      requestId: req.requestId || 'unknown',
      timestamp: new Date().toISOString()
    });
  }

  // Store validated data for use in controller
  req.validatedData = value;
  next();
};

module.exports = {
  validateVendorMarginCreation,
  validateVendorMarginUpdate,
  validateVendorMarginCreationMiddleware,
  validateVendorMarginUpdateMiddleware
};