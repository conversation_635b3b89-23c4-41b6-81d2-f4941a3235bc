const vendorService = require('../services/vendorService');
const { asyncHandler } = require('../middleware/errorHandler');

/**
 * Vendor Controller
 * Handles HTTP requests for vendor management operations
 */

/**
 * Create a new vendor
 * POST /vendors
 */
const createVendor = asyncHandler(async (req, res, next) => {
  const vendorData = req.validatedData || req.body;

  // Add creator information if available from auth middleware
  if (req.user && req.user.userId) {
    vendorData.createdBy = req.user.userId;
  }

  const result = await vendorService.createVendor(vendorData);

  res.status(201).json({
    success: true,
    message: result.message,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get all vendors with pagination and filtering
 * GET /vendors
 */
const getVendors = asyncHandler(async (req, res, next) => {
  const { page = 1, limit = 10, status, type, city, state, search } = req.query;

  const filters = {};
  if (status) filters.status = status;
  if (type) filters.type = type;
  if (city) filters.city = city;
  if (state) filters.state = state;
  if (search) filters.search = search;

  const result = await vendorService.getVendors(page, limit, filters);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.vendors,
    pagination: result.pagination,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get vendor by ID
 * GET /vendors/:id
 */
const getVendorById = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const result = await vendorService.getVendorById(id);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.vendor,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Update vendor by ID
 * PUT /vendors/:id
 */
const updateVendor = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const updateData = req.validatedData || req.body;
  const modifiedBy = req.user?.userId || 'SYSTEM';

  const result = await vendorService.updateVendor(id, updateData, modifiedBy);

  res.status(200).json({
    success: true,
    message: result.message,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Delete vendor by ID
 * DELETE /vendors/:id
 */
const deleteVendor = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const result = await vendorService.deleteVendor(id);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.vendor,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Get vendors by status
 * GET /vendors/status/:status
 */
const getVendorsByStatus = asyncHandler(async (req, res, next) => {
  const { status } = req.params;
  const { page = 1, limit = 10 } = req.query;

  const result = await vendorService.getVendorsByStatus(status, page, limit);

  res.status(200).json({
    success: true,
    message: result.message,
    data: result.vendors,
    pagination: result.pagination,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

/**
 * Update vendor status
 * PATCH /vendors/:id/status
 */
const updateVendorStatus = asyncHandler(async (req, res, next) => {
  const { id } = req.params;
  const { status, remarks } = req.body;

  // Get user ID from auth middleware
  const userId = req.user && req.user.userId ? req.user.userId : 'system';

  const result = await vendorService.updateVendorStatus(id, status, userId, remarks);

  res.status(200).json({
    success: true,
    message: result.message,
    requestId: req.requestId,
    timestamp: new Date().toISOString()
  });
});

module.exports = {
  createVendor,
  getVendors,
  getVendorById,
  updateVendor,
  deleteVendor,
  getVendorsByStatus,
  updateVendorStatus
};