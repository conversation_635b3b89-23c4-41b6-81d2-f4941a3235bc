const express = require('express');
const router = express.Router();
const sellerPriceConfigController = require('../controller/sellerPriceConfigController');

/**
 * Seller Price Config Routes
 * Handles routing for seller price configuration management operations
 */

/**
 * @route   POST /seller-price-config
 * @desc    Create a new seller price config
 * @access  Private
 */
router.post('/', sellerPriceConfigController.createSellerPriceConfig);

/**
 * @route   GET /seller-price-config
 * @desc    Get all seller price configs with pagination and filtering
 * @access  Private
 */
router.get('/', sellerPriceConfigController.getSellerPriceConfigs);

/**
 * @route   GET /seller-price-config/seller/:sellerId
 * @desc    Get seller price configs by seller ID
 * @access  Private
 */
router.get('/seller/:sellerId', sellerPriceConfigController.getConfigsBySeller);

/**
 * @route   GET /seller-price-config/product/:productId
 * @desc    Get seller price configs by product ID
 * @access  Private
 */
router.get('/product/:productId', sellerPriceConfigController.getConfigsByProduct);

/**
 * @route   GET /seller-price-config/:id
 * @desc    Get seller price config by ID
 * @access  Private
 */
router.get('/:id', sellerPriceConfigController.getSellerPriceConfigById);

/**
 * @route   PUT /seller-price-config/:id
 * @desc    Update seller price config by ID
 * @access  Private
 */
router.put('/:id', sellerPriceConfigController.updateSellerPriceConfig);

/**
 * @route   DELETE /seller-price-config/:id
 * @desc    Delete seller price config by ID
 * @access  Private
 */
router.delete('/:id', sellerPriceConfigController.deleteSellerPriceConfig);

module.exports = router;
