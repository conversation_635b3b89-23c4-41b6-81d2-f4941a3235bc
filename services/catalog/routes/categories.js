const express = require('express');
const router = express.Router();
const categoryController = require('../controller/categoryController');

/**
 * Category Routes
 * Handles routing for category management operations
 */

/**
 * @route   POST /categories
 * @desc    Create a new category
 * @access  Private
 */
router.post('/', categoryController.createCategory);

/**
 * @route   GET /categories
 * @desc    Get all categories with pagination and filtering
 * @access  Public
 */
router.get('/', categoryController.getCategories);

/**
 * @route   GET /categories/tree
 * @desc    Get category tree/hierarchy
 * @access  Public
 */
router.get('/tree', categoryController.getCategoryTree);

/**
 * @route   GET /categories/:id/subcategories
 * @desc    Get subcategories by parent ID
 * @access  Public
 */
router.get('/:id/subcategories', categoryController.getSubcategories);

/**
 * @route   GET /categories/:id
 * @desc    Get category by ID
 * @access  Public
 */
router.get('/:id', categoryController.getCategoryById);

/**
 * @route   PUT /categories/:id
 * @desc    Update category by ID
 * @access  Private
 */
router.put('/:id', categoryController.updateCategory);

/**
 * @route   DELETE /categories/:id
 * @desc    Delete category by ID
 * @access  Private
 */
router.delete('/:id', categoryController.deleteCategory);

module.exports = router;
