const express = require('express');
const router = express.Router();
const productController = require('../controller/productController');

/**
 * Product Routes
 * Handles routing for product management operations
 */

/**
 * @route   POST /products
 * @desc    Create a new product
 * @access  Private
 */
router.post('/', productController.createProduct);

/**
 * @route   GET /products
 * @desc    Get all products with pagination and filtering
 * @access  Public
 */
router.get('/', productController.getProducts);

/**
 * @route   GET /products/category/:categoryId
 * @desc    Get products by category
 * @access  Public
 */
router.get('/category/:categoryId', productController.getProductsByCategory);

/**
 * @route   GET /products/brand/:brandId
 * @desc    Get products by brand
 * @access  Public
 */
router.get('/brand/:brandId', productController.getProductsByBrand);

/**
 * @route   GET /products/:id
 * @desc    Get product by ID
 * @access  Public
 */
router.get('/:id', productController.getProductById);

/**
 * @route   PUT /products/:id
 * @desc    Update product by ID
 * @access  Private
 */
router.put('/:id', productController.updateProduct);

/**
 * @route   DELETE /products/:id
 * @desc    Delete product by ID
 * @access  Private
 */
router.delete('/:id', productController.deleteProduct);

module.exports = router;
