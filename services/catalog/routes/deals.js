const express = require('express');
const router = express.Router();
const dealController = require('../controller/dealController');

/**
 * Deal Routes
 * Handles routing for deal management operations
 */

/**
 * @route   POST /deals
 * @desc    Create a new deal
 * @access  Private
 */
router.post('/', dealController.createDeal);

/**
 * @route   GET /deals
 * @desc    Get all deals with pagination and filtering
 * @access  Public
 */
router.get('/', dealController.getDeals);

/**
 * @route   GET /deals/active
 * @desc    Get active deals
 * @access  Public
 */
router.get('/active', dealController.getActiveDeals);

/**
 * @route   GET /deals/:id
 * @desc    Get deal by ID
 * @access  Public
 */
router.get('/:id', dealController.getDealById);

/**
 * @route   PUT /deals/:id
 * @desc    Update deal by ID
 * @access  Private
 */
router.put('/:id', dealController.updateDeal);

/**
 * @route   DELETE /deals/:id
 * @desc    Delete deal by ID
 * @access  Private
 */
router.delete('/:id', dealController.deleteDeal);

module.exports = router;
