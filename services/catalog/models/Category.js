const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Category ID with CT prefix
 */
const generateCategoryId = () => {
  return `CT${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Category Schema for Catalog Management System
 * Implements hierarchical category structure
 */
const categorySchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateCategoryId
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
    index: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  parentId: {
    type: String,
    default: null,
    index: true
  },
  level: {
    type: Number,
    required: true,
    default: 0,
    min: 0,
    max: 5,
    index: true
  },
  path: {
    type: String,
    required: true,
    index: true
  },
  image: {
    url: String,
    altText: String
  },
  icon: {
    type: String,
    trim: true
  },
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'draft'],
    default: 'active',
    index: true
  },
  featured: {
    type: Boolean,
    default: false,
    index: true
  },
  sortOrder: {
    type: Number,
    default: 0,
    index: true
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: {
      type: String,
      unique: true,
      sparse: true,
      lowercase: true
    },
    keywords: [String]
  },
  attributes: [{
    name: {
      type: String,
      required: true,
      trim: true
    },
    type: {
      type: String,
      required: true,
      enum: ['text', 'number', 'boolean', 'select', 'multiselect'],
      default: 'text'
    },
    required: {
      type: Boolean,
      default: false
    },
    options: [String], // For select/multiselect types
    unit: String // For number types
  }],
  statistics: {
    totalProducts: {
      type: Number,
      default: 0,
      min: 0
    },
    totalSubcategories: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'categories'
});

// Indexes for better query performance
categorySchema.index({ name: 'text', description: 'text' });
categorySchema.index({ parentId: 1, status: 1, sortOrder: 1 });
categorySchema.index({ level: 1, status: 1 });
categorySchema.index({ path: 1 });
categorySchema.index({ featured: 1, status: 1 });
categorySchema.index({ createdAt: -1 });

// Virtual for checking if category has children
categorySchema.virtual('hasChildren').get(function() {
  return this.statistics.totalSubcategories > 0;
});

// Pre-save middleware to generate slug and path
categorySchema.pre('save', async function(next) {
  // Generate slug if name is modified
  if (this.isModified('name') && !this.seo.slug) {
    this.seo.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  // Generate path and level based on parent
  if (this.isModified('parentId') || this.isNew) {
    if (this.parentId) {
      const parent = await this.constructor.findById(this.parentId);
      if (!parent) {
        return next(new Error('Parent category not found'));
      }
      
      this.level = parent.level + 1;
      this.path = `${parent.path}/${this._id}`;
      
      // Check for circular reference
      if (parent.path.includes(this._id)) {
        return next(new Error('Circular reference detected'));
      }
    } else {
      this.level = 0;
      this.path = this._id;
    }
  }
  
  next();
});

// Post-save middleware to update parent statistics
categorySchema.post('save', async function() {
  if (this.parentId) {
    await this.constructor.updateParentStatistics(this.parentId);
  }
});

// Post-remove middleware to update parent statistics
categorySchema.post('remove', async function() {
  if (this.parentId) {
    await this.constructor.updateParentStatistics(this.parentId);
  }
});

// Static method to update parent statistics
categorySchema.statics.updateParentStatistics = async function(parentId) {
  const subcategoriesCount = await this.countDocuments({
    parentId: parentId,
    status: 'active'
  });
  
  await this.findByIdAndUpdate(parentId, {
    'statistics.totalSubcategories': subcategoriesCount
  });
};

// Static method to get category tree
categorySchema.statics.getCategoryTree = async function(parentId = null, maxLevel = 3) {
  const categories = await this.find({
    parentId: parentId,
    status: 'active'
  }).sort({ sortOrder: 1, name: 1 });
  
  const tree = [];
  
  for (const category of categories) {
    const categoryObj = category.toObject();
    
    if (category.level < maxLevel) {
      categoryObj.children = await this.getCategoryTree(category._id, maxLevel);
    }
    
    tree.push(categoryObj);
  }
  
  return tree;
};

// Static method to find all descendants
categorySchema.statics.findDescendants = function(categoryId) {
  return this.find({
    path: new RegExp(`^.*/${categoryId}/.*$|^${categoryId}/.*$`),
    status: 'active'
  });
};

// Static method to find all ancestors
categorySchema.statics.findAncestors = async function(categoryId) {
  const category = await this.findById(categoryId);
  if (!category) return [];
  
  const pathIds = category.path.split('/').filter(id => id !== categoryId);
  return this.find({
    _id: { $in: pathIds },
    status: 'active'
  }).sort({ level: 1 });
};

// Method to update product statistics
categorySchema.methods.updateProductStatistics = async function() {
  const Product = mongoose.model('Product');
  
  const productCount = await Product.countDocuments({
    'category.categoryId': this._id,
    status: 'active'
  });
  
  this.statistics.totalProducts = productCount;
  return this.save();
};

const Category = mongoose.model('Category', categorySchema);

module.exports = Category;
