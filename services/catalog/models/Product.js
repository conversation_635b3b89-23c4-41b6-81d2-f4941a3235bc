const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Product ID with PR prefix
 */
const generateProductId = () => {
  return `PR${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Product Schema for Catalog Management System
 * Implements comprehensive product data structure
 */
const productSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateProductId
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200,
    index: true
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 2000
  },
  shortDescription: {
    type: String,
    trim: true,
    maxlength: 500
  },
  sku: {
    type: String,
    required: true,
    unique: true,
    trim: true,
    uppercase: true,
    index: true
  },
  barcode: {
    type: String,
    trim: true,
    index: true
  },
  category: {
    categoryId: {
      type: String,
      required: true,
      index: true
    },
    categoryName: {
      type: String,
      required: true
    },
    categoryPath: {
      type: String,
      required: true
    }
  },
  brand: {
    brandId: {
      type: String,
      required: true,
      index: true
    },
    brandName: {
      type: String,
      required: true
    }
  },
  pricing: {
    basePrice: {
      type: Number,
      required: true,
      min: 0
    },
    sellingPrice: {
      type: Number,
      required: true,
      min: 0
    },
    costPrice: {
      type: Number,
      min: 0
    },
    currency: {
      type: String,
      default: 'INR',
      enum: ['INR', 'USD', 'EUR']
    }
  },
  inventory: {
    stockQuantity: {
      type: Number,
      required: true,
      min: 0,
      default: 0
    },
    lowStockThreshold: {
      type: Number,
      default: 10,
      min: 0
    },
    trackInventory: {
      type: Boolean,
      default: true
    }
  },
  specifications: {
    weight: {
      value: Number,
      unit: {
        type: String,
        enum: ['kg', 'g', 'lb', 'oz'],
        default: 'kg'
      }
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: {
        type: String,
        enum: ['cm', 'inch', 'm'],
        default: 'cm'
      }
    },
    color: String,
    size: String,
    material: String,
    model: String
  },
  images: [{
    url: {
      type: String,
      required: true
    },
    altText: String,
    isPrimary: {
      type: Boolean,
      default: false
    },
    order: {
      type: Number,
      default: 0
    }
  }],
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  status: {
    type: String,
    required: true,
    enum: ['draft', 'active', 'inactive', 'discontinued'],
    default: 'draft',
    index: true
  },
  visibility: {
    type: String,
    required: true,
    enum: ['public', 'private', 'hidden'],
    default: 'public'
  },
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: {
      type: String,
      unique: true,
      sparse: true,
      lowercase: true
    }
  },
  ratings: {
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalReviews: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'products'
});

// Indexes for better query performance
productSchema.index({ name: 'text', description: 'text', tags: 'text' });
productSchema.index({ 'category.categoryId': 1, status: 1 });
productSchema.index({ 'brand.brandId': 1, status: 1 });
productSchema.index({ status: 1, visibility: 1 });
productSchema.index({ 'pricing.sellingPrice': 1 });
productSchema.index({ createdAt: -1 });

// Virtual for checking if product is in stock
productSchema.virtual('isInStock').get(function() {
  return !this.inventory.trackInventory || this.inventory.stockQuantity > 0;
});

// Virtual for checking if product is low stock
productSchema.virtual('isLowStock').get(function() {
  return this.inventory.trackInventory && 
         this.inventory.stockQuantity <= this.inventory.lowStockThreshold;
});

// Virtual for primary image
productSchema.virtual('primaryImage').get(function() {
  return this.images.find(img => img.isPrimary) || this.images[0];
});

// Pre-save middleware to generate slug
productSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.seo.slug) {
    this.seo.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  
  // Ensure only one primary image
  if (this.images && this.images.length > 0) {
    const primaryImages = this.images.filter(img => img.isPrimary);
    if (primaryImages.length > 1) {
      this.images.forEach((img, index) => {
        img.isPrimary = index === 0;
      });
    } else if (primaryImages.length === 0) {
      this.images[0].isPrimary = true;
    }
  }
  
  next();
});

// Static method to find products by category
productSchema.statics.findByCategory = function(categoryId, options = {}) {
  return this.find({
    'category.categoryId': categoryId,
    status: 'active',
    visibility: 'public',
    ...options
  });
};

// Static method to find products by brand
productSchema.statics.findByBrand = function(brandId, options = {}) {
  return this.find({
    'brand.brandId': brandId,
    status: 'active',
    visibility: 'public',
    ...options
  });
};

const Product = mongoose.model('Product', productSchema);

module.exports = Product;
