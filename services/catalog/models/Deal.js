const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Deal ID with DL prefix
 */
const generateDealId = () => {
  return `DL${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Deal Schema for Catalog Management System
 * Implements comprehensive deal data structure
 */
const dealSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateDealId
  },
  title: {
    type: String,
    required: true,
    trim: true,
    maxlength: 200
  },
  description: {
    type: String,
    required: true,
    trim: true,
    maxlength: 1000
  },
  dealType: {
    type: String,
    required: true,
    enum: ['percentage', 'fixed_amount', 'buy_one_get_one', 'bundle'],
    default: 'percentage'
  },
  discountValue: {
    type: Number,
    required: true,
    min: 0
  },
  minimumOrderValue: {
    type: Number,
    default: 0,
    min: 0
  },
  maximumDiscountAmount: {
    type: Number,
    min: 0
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    required: true,
    enum: ['draft', 'active', 'paused', 'expired', 'cancelled'],
    default: 'draft',
    index: true
  },
  applicableProducts: [{
    productId: {
      type: String,
      required: true
    },
    productName: {
      type: String,
      required: true
    }
  }],
  applicableCategories: [{
    categoryId: {
      type: String,
      required: true
    },
    categoryName: {
      type: String,
      required: true
    }
  }],
  applicableBrands: [{
    brandId: {
      type: String,
      required: true
    },
    brandName: {
      type: String,
      required: true
    }
  }],
  usageLimit: {
    type: Number,
    min: 1
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  userLimit: {
    type: Number,
    min: 1
  },
  isStackable: {
    type: Boolean,
    default: false
  },
  priority: {
    type: Number,
    default: 1,
    min: 1,
    max: 10
  },
  terms: {
    type: String,
    maxlength: 2000
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'deals'
});

// Indexes for better query performance
dealSchema.index({ status: 1, startDate: 1, endDate: 1 });
dealSchema.index({ 'applicableCategories.categoryId': 1 });
dealSchema.index({ 'applicableBrands.brandId': 1 });
dealSchema.index({ 'applicableProducts.productId': 1 });
dealSchema.index({ createdAt: -1 });

// Virtual for checking if deal is currently active
dealSchema.virtual('isCurrentlyActive').get(function() {
  const now = new Date();
  return this.status === 'active' && 
         this.startDate <= now && 
         this.endDate >= now &&
         (this.usageLimit ? this.usageCount < this.usageLimit : true);
});

// Pre-save middleware to validate dates
dealSchema.pre('save', function(next) {
  if (this.startDate >= this.endDate) {
    return next(new Error('End date must be after start date'));
  }
  
  if (this.dealType === 'percentage' && this.discountValue > 100) {
    return next(new Error('Percentage discount cannot exceed 100%'));
  }
  
  next();
});

// Method to check if deal is applicable to a product
dealSchema.methods.isApplicableToProduct = function(productId, categoryId, brandId) {
  // Check if specific product is included
  if (this.applicableProducts.some(p => p.productId === productId)) {
    return true;
  }
  
  // Check if product's category is included
  if (categoryId && this.applicableCategories.some(c => c.categoryId === categoryId)) {
    return true;
  }
  
  // Check if product's brand is included
  if (brandId && this.applicableBrands.some(b => b.brandId === brandId)) {
    return true;
  }
  
  return false;
};

// Static method to find active deals
dealSchema.statics.findActiveDeals = function() {
  const now = new Date();
  return this.find({
    status: 'active',
    startDate: { $lte: now },
    endDate: { $gte: now }
  });
};

const Deal = mongoose.model('Deal', dealSchema);

module.exports = Deal;
