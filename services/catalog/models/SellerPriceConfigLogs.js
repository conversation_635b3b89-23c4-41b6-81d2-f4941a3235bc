const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Seller Price Config Log ID with SPCL prefix
 */
const generateSellerPriceConfigLogId = () => {
  return `SPCL${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Seller Price Config Logs Schema for Catalog Management System
 * Implements audit trail for seller price configuration changes
 */
const sellerPriceConfigLogsSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateSellerPriceConfigLogId
  },
  configId: {
    type: String,
    required: true,
    index: true
  },
  sellerId: {
    type: String,
    required: true,
    index: true
  },
  sellerName: {
    type: String,
    required: true,
    trim: true
  },
  productId: {
    type: String,
    required: true,
    index: true
  },
  productName: {
    type: String,
    required: true,
    trim: true
  },
  action: {
    type: String,
    required: true,
    enum: [
      'created',
      'updated',
      'approved',
      'rejected',
      'activated',
      'deactivated',
      'suspended',
      'price_changed',
      'inventory_updated',
      'commission_changed',
      'deleted'
    ],
    index: true
  },
  changes: {
    field: {
      type: String,
      required: true
    },
    oldValue: mongoose.Schema.Types.Mixed,
    newValue: mongoose.Schema.Types.Mixed,
    changeType: {
      type: String,
      enum: ['create', 'update', 'delete'],
      required: true
    }
  },
  previousData: {
    pricing: {
      basePrice: Number,
      sellerPrice: Number,
      costPrice: Number,
      marginPercentage: Number
    },
    inventory: {
      stockQuantity: Number,
      reservedQuantity: Number,
      availableQuantity: Number
    },
    commission: {
      type: String,
      value: Number,
      calculatedAmount: Number
    },
    status: String
  },
  currentData: {
    pricing: {
      basePrice: Number,
      sellerPrice: Number,
      costPrice: Number,
      marginPercentage: Number
    },
    inventory: {
      stockQuantity: Number,
      reservedQuantity: Number,
      availableQuantity: Number
    },
    commission: {
      type: String,
      value: Number,
      calculatedAmount: Number
    },
    status: String
  },
  reason: {
    type: String,
    trim: true,
    maxlength: 500
  },
  metadata: {
    userAgent: String,
    ipAddress: String,
    source: {
      type: String,
      enum: ['web', 'mobile', 'api', 'system', 'bulk_import'],
      default: 'web'
    },
    batchId: String, // For bulk operations
    correlationId: String // For tracking related operations
  },
  impact: {
    priceChange: {
      amount: Number,
      percentage: Number
    },
    inventoryChange: {
      quantity: Number,
      type: {
        type: String,
        enum: ['increase', 'decrease']
      }
    },
    commissionChange: {
      amount: Number,
      percentage: Number
    }
  },
  performedBy: {
    userId: {
      type: String,
      required: true
    },
    userName: {
      type: String,
      required: true
    },
    userType: {
      type: String,
      enum: ['admin', 'seller', 'system', 'api'],
      required: true
    }
  },
  timestamp: {
    type: Date,
    default: Date.now,
    index: true
  },
  version: {
    type: Number,
    default: 1,
    min: 1
  }
}, {
  timestamps: false, // Using custom timestamp field
  collection: 'seller_price_config_logs'
});

// Indexes for better query performance
sellerPriceConfigLogsSchema.index({ configId: 1, timestamp: -1 });
sellerPriceConfigLogsSchema.index({ sellerId: 1, timestamp: -1 });
sellerPriceConfigLogsSchema.index({ productId: 1, timestamp: -1 });
sellerPriceConfigLogsSchema.index({ action: 1, timestamp: -1 });
sellerPriceConfigLogsSchema.index({ 'performedBy.userId': 1, timestamp: -1 });
sellerPriceConfigLogsSchema.index({ 'metadata.batchId': 1 });
sellerPriceConfigLogsSchema.index({ timestamp: -1 });

// Virtual for calculating time since change
sellerPriceConfigLogsSchema.virtual('timeSinceChange').get(function() {
  return Date.now() - this.timestamp.getTime();
});

// Virtual for checking if change is recent (within last hour)
sellerPriceConfigLogsSchema.virtual('isRecentChange').get(function() {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  return this.timestamp > oneHourAgo;
});

// Static method to create log entry
sellerPriceConfigLogsSchema.statics.createLog = async function(logData) {
  const log = new this(logData);
  return log.save();
};

// Static method to get logs for a specific config
sellerPriceConfigLogsSchema.statics.getConfigHistory = function(configId, options = {}) {
  const { limit = 50, skip = 0, action } = options;
  
  const query = { configId };
  if (action) {
    query.action = action;
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to get logs for a seller
sellerPriceConfigLogsSchema.statics.getSellerHistory = function(sellerId, options = {}) {
  const { limit = 100, skip = 0, startDate, endDate, action } = options;
  
  const query = { sellerId };
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = new Date(startDate);
    if (endDate) query.timestamp.$lte = new Date(endDate);
  }
  
  if (action) {
    query.action = action;
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to get audit trail for a product
sellerPriceConfigLogsSchema.statics.getProductAuditTrail = function(productId, options = {}) {
  const { limit = 100, skip = 0, startDate, endDate } = options;
  
  const query = { productId };
  
  if (startDate || endDate) {
    query.timestamp = {};
    if (startDate) query.timestamp.$gte = new Date(startDate);
    if (endDate) query.timestamp.$lte = new Date(endDate);
  }
  
  return this.find(query)
    .sort({ timestamp: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to get summary statistics
sellerPriceConfigLogsSchema.statics.getSummaryStats = async function(filters = {}) {
  const { sellerId, productId, startDate, endDate } = filters;
  
  const matchStage = {};
  if (sellerId) matchStage.sellerId = sellerId;
  if (productId) matchStage.productId = productId;
  
  if (startDate || endDate) {
    matchStage.timestamp = {};
    if (startDate) matchStage.timestamp.$gte = new Date(startDate);
    if (endDate) matchStage.timestamp.$lte = new Date(endDate);
  }
  
  const pipeline = [
    { $match: matchStage },
    {
      $group: {
        _id: '$action',
        count: { $sum: 1 },
        lastOccurrence: { $max: '$timestamp' }
      }
    },
    { $sort: { count: -1 } }
  ];
  
  return this.aggregate(pipeline);
};

// Method to format log for display
sellerPriceConfigLogsSchema.methods.formatForDisplay = function() {
  return {
    id: this._id,
    action: this.action,
    field: this.changes.field,
    oldValue: this.changes.oldValue,
    newValue: this.changes.newValue,
    reason: this.reason,
    performedBy: this.performedBy.userName,
    timestamp: this.timestamp,
    impact: this.impact
  };
};

const SellerPriceConfigLogs = mongoose.model('SellerPriceConfigLogs', sellerPriceConfigLogsSchema);

module.exports = SellerPriceConfigLogs;
