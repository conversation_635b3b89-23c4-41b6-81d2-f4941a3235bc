const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Seller Deal ID with SD prefix
 */
const generateSellerDealId = () => {
  return `SD${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Seller Deal Schema for Catalog Management System
 * Implements seller-specific deal configurations
 */
const sellerDealSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateSellerDealId
  },
  sellerId: {
    type: String,
    required: true,
    index: true
  },
  sellerName: {
    type: String,
    required: true,
    trim: true
  },
  dealId: {
    type: String,
    required: true,
    index: true
  },
  dealTitle: {
    type: String,
    required: true,
    trim: true
  },
  customTitle: {
    type: String,
    trim: true,
    maxlength: 200
  },
  customDescription: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  sellerDiscountValue: {
    type: Number,
    min: 0
  },
  sellerMinimumOrderValue: {
    type: Number,
    default: 0,
    min: 0
  },
  sellerMaximumDiscountAmount: {
    type: Number,
    min: 0
  },
  commission: {
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    },
    value: {
      type: Number,
      required: true,
      min: 0
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'active', 'paused', 'rejected', 'expired'],
    default: 'pending',
    index: true
  },
  approvalDate: {
    type: Date
  },
  approvedBy: {
    type: String
  },
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: 500
  },
  sellerProducts: [{
    productId: {
      type: String,
      required: true
    },
    productName: {
      type: String,
      required: true
    },
    sellerPrice: {
      type: Number,
      required: true,
      min: 0
    },
    dealPrice: {
      type: Number,
      required: true,
      min: 0
    },
    stockQuantity: {
      type: Number,
      required: true,
      min: 0
    }
  }],
  usageLimit: {
    type: Number,
    min: 1
  },
  usageCount: {
    type: Number,
    default: 0,
    min: 0
  },
  revenue: {
    totalSales: {
      type: Number,
      default: 0,
      min: 0
    },
    totalCommission: {
      type: Number,
      default: 0,
      min: 0
    },
    totalOrders: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  terms: {
    type: String,
    maxlength: 2000
  },
  notes: {
    type: String,
    maxlength: 1000
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'seller_deals'
});

// Indexes for better query performance
sellerDealSchema.index({ sellerId: 1, status: 1 });
sellerDealSchema.index({ dealId: 1, status: 1 });
sellerDealSchema.index({ sellerId: 1, dealId: 1 }, { unique: true });
sellerDealSchema.index({ status: 1, approvalDate: 1 });
sellerDealSchema.index({ 'sellerProducts.productId': 1 });
sellerDealSchema.index({ createdAt: -1 });

// Virtual for checking if seller deal is active
sellerDealSchema.virtual('isActive').get(function() {
  return this.status === 'active' &&
         (this.usageLimit ? this.usageCount < this.usageLimit : true);
});

// Virtual for calculating total discount amount
sellerDealSchema.virtual('totalDiscountAmount').get(function() {
  return this.sellerProducts.reduce((total, product) => {
    return total + (product.sellerPrice - product.dealPrice) * product.stockQuantity;
  }, 0);
});

// Pre-save middleware to validate seller deal
sellerDealSchema.pre('save', function(next) {
  // Validate that deal price is less than seller price
  for (const product of this.sellerProducts) {
    if (product.dealPrice >= product.sellerPrice) {
      return next(new Error(`Deal price must be less than seller price for product ${product.productName}`));
    }
  }
  
  // Set approval date when status changes to approved
  if (this.isModified('status') && this.status === 'approved' && !this.approvalDate) {
    this.approvalDate = new Date();
  }
  
  next();
});

// Method to calculate commission amount
sellerDealSchema.methods.calculateCommission = function(orderAmount) {
  if (this.commission.type === 'percentage') {
    return (orderAmount * this.commission.value) / 100;
  } else {
    return this.commission.value;
  }
};

// Method to update usage statistics
sellerDealSchema.methods.updateUsage = function(orderAmount, commissionAmount) {
  this.usageCount += 1;
  this.revenue.totalSales += orderAmount;
  this.revenue.totalCommission += commissionAmount;
  this.revenue.totalOrders += 1;
  
  return this.save();
};

// Static method to find active seller deals
sellerDealSchema.statics.findActiveBySeller = function(sellerId) {
  return this.find({
    sellerId: sellerId,
    status: 'active'
  });
};

// Static method to find seller deals by deal ID
sellerDealSchema.statics.findByDeal = function(dealId) {
  return this.find({
    dealId: dealId,
    status: { $in: ['approved', 'active'] }
  });
};

const SellerDeal = mongoose.model('SellerDeal', sellerDealSchema);

module.exports = SellerDeal;
