const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Seller Price Config ID with SPC prefix
 */
const generateSellerPriceConfigId = () => {
  return `SPC${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Seller Price Config Schema for Catalog Management System
 * Implements seller-specific pricing configurations
 */
const sellerPriceConfigSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateSellerPriceConfigId
  },
  sellerId: {
    type: String,
    required: true,
    index: true
  },
  sellerName: {
    type: String,
    required: true,
    trim: true
  },
  productId: {
    type: String,
    required: true,
    index: true
  },
  productName: {
    type: String,
    required: true,
    trim: true
  },
  sku: {
    type: String,
    required: true,
    trim: true,
    uppercase: true
  },
  pricing: {
    basePrice: {
      type: Number,
      required: true,
      min: 0
    },
    sellerPrice: {
      type: Number,
      required: true,
      min: 0
    },
    costPrice: {
      type: Number,
      min: 0
    },
    marginPercentage: {
      type: Number,
      min: 0,
      max: 100
    },
    currency: {
      type: String,
      default: 'INR',
      enum: ['INR', 'USD', 'EUR']
    }
  },
  inventory: {
    stockQuantity: {
      type: Number,
      required: true,
      min: 0,
      default: 0
    },
    reservedQuantity: {
      type: Number,
      default: 0,
      min: 0
    },
    availableQuantity: {
      type: Number,
      default: 0,
      min: 0
    },
    lowStockThreshold: {
      type: Number,
      default: 10,
      min: 0
    },
    maxOrderQuantity: {
      type: Number,
      min: 1
    },
    minOrderQuantity: {
      type: Number,
      default: 1,
      min: 1
    }
  },
  commission: {
    type: {
      type: String,
      enum: ['percentage', 'fixed'],
      default: 'percentage'
    },
    value: {
      type: Number,
      required: true,
      min: 0
    },
    calculatedAmount: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  shipping: {
    weight: {
      value: Number,
      unit: {
        type: String,
        enum: ['kg', 'g', 'lb', 'oz'],
        default: 'kg'
      }
    },
    dimensions: {
      length: Number,
      width: Number,
      height: Number,
      unit: {
        type: String,
        enum: ['cm', 'inch', 'm'],
        default: 'cm'
      }
    },
    shippingCost: {
      type: Number,
      default: 0,
      min: 0
    },
    freeShippingThreshold: {
      type: Number,
      min: 0
    }
  },
  status: {
    type: String,
    required: true,
    enum: ['pending', 'approved', 'active', 'inactive', 'rejected', 'suspended'],
    default: 'pending',
    index: true
  },
  approvalDate: {
    type: Date
  },
  approvedBy: {
    type: String
  },
  rejectionReason: {
    type: String,
    trim: true,
    maxlength: 500
  },
  effectiveDate: {
    type: Date,
    default: Date.now
  },
  expiryDate: {
    type: Date
  },
  priority: {
    type: Number,
    default: 1,
    min: 1,
    max: 10
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  notes: {
    type: String,
    maxlength: 1000
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'seller_price_configs'
});

// Compound indexes for better query performance
sellerPriceConfigSchema.index({ sellerId: 1, productId: 1 }, { unique: true });
sellerPriceConfigSchema.index({ sellerId: 1, status: 1 });
sellerPriceConfigSchema.index({ productId: 1, status: 1 });
sellerPriceConfigSchema.index({ status: 1, effectiveDate: 1 });
sellerPriceConfigSchema.index({ 'pricing.sellerPrice': 1 });
sellerPriceConfigSchema.index({ priority: -1, createdAt: -1 });

// Virtual for checking if config is currently active
sellerPriceConfigSchema.virtual('isCurrentlyActive').get(function() {
  const now = new Date();
  return this.status === 'active' &&
         this.effectiveDate <= now &&
         (!this.expiryDate || this.expiryDate >= now);
});

// Virtual for checking if product is in stock
sellerPriceConfigSchema.virtual('isInStock').get(function() {
  return this.inventory.availableQuantity > 0;
});

// Virtual for checking if product is low stock
sellerPriceConfigSchema.virtual('isLowStock').get(function() {
  return this.inventory.availableQuantity <= this.inventory.lowStockThreshold;
});

// Pre-save middleware to calculate derived values
sellerPriceConfigSchema.pre('save', function(next) {
  // Calculate available quantity
  this.inventory.availableQuantity = Math.max(0, 
    this.inventory.stockQuantity - this.inventory.reservedQuantity
  );
  
  // Calculate margin percentage
  if (this.pricing.costPrice && this.pricing.sellerPrice) {
    this.pricing.marginPercentage = Math.round(
      ((this.pricing.sellerPrice - this.pricing.costPrice) / this.pricing.sellerPrice) * 100 * 100
    ) / 100;
  }
  
  // Calculate commission amount
  if (this.commission.type === 'percentage') {
    this.commission.calculatedAmount = (this.pricing.sellerPrice * this.commission.value) / 100;
  } else {
    this.commission.calculatedAmount = this.commission.value;
  }
  
  // Set approval date when status changes to approved
  if (this.isModified('status') && this.status === 'approved' && !this.approvalDate) {
    this.approvalDate = new Date();
  }
  
  // Validate price relationships
  if (this.pricing.costPrice && this.pricing.sellerPrice <= this.pricing.costPrice) {
    return next(new Error('Seller price must be greater than cost price'));
  }
  
  if (this.pricing.sellerPrice < this.pricing.basePrice * 0.8) {
    return next(new Error('Seller price cannot be less than 80% of base price'));
  }
  
  next();
});

// Method to update inventory
sellerPriceConfigSchema.methods.updateInventory = function(quantityChange, type = 'stock') {
  if (type === 'stock') {
    this.inventory.stockQuantity = Math.max(0, this.inventory.stockQuantity + quantityChange);
  } else if (type === 'reserved') {
    this.inventory.reservedQuantity = Math.max(0, this.inventory.reservedQuantity + quantityChange);
  }
  
  this.inventory.availableQuantity = Math.max(0, 
    this.inventory.stockQuantity - this.inventory.reservedQuantity
  );
  
  return this.save();
};

// Method to check if quantity is available for order
sellerPriceConfigSchema.methods.isQuantityAvailable = function(requestedQuantity) {
  return this.inventory.availableQuantity >= requestedQuantity &&
         (!this.inventory.maxOrderQuantity || requestedQuantity <= this.inventory.maxOrderQuantity) &&
         requestedQuantity >= this.inventory.minOrderQuantity;
};

// Static method to find active configs by seller
sellerPriceConfigSchema.statics.findActiveBySeller = function(sellerId) {
  const now = new Date();
  return this.find({
    sellerId: sellerId,
    status: 'active',
    effectiveDate: { $lte: now },
    $or: [
      { expiryDate: { $exists: false } },
      { expiryDate: null },
      { expiryDate: { $gte: now } }
    ]
  });
};

// Static method to find configs by product
sellerPriceConfigSchema.statics.findByProduct = function(productId, activeOnly = true) {
  const query = { productId: productId };
  
  if (activeOnly) {
    const now = new Date();
    query.status = 'active';
    query.effectiveDate = { $lte: now };
    query.$or = [
      { expiryDate: { $exists: false } },
      { expiryDate: null },
      { expiryDate: { $gte: now } }
    ];
  }
  
  return this.find(query).sort({ priority: -1, 'pricing.sellerPrice': 1 });
};

const SellerPriceConfig = mongoose.model('SellerPriceConfig', sellerPriceConfigSchema);

module.exports = SellerPriceConfig;
