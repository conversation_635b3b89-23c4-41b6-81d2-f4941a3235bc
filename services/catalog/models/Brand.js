const mongoose = require('mongoose');
const { v4: uuidv4 } = require('uuid');

/**
 * Generate Brand ID with BR prefix
 */
const generateBrandId = () => {
  return `BR${uuidv4().replace(/-/g, '').substring(0,4).toUpperCase()}`;
};

/**
 * Brand Schema for Catalog Management System
 * Implements comprehensive brand data structure
 */
const brandSchema = new mongoose.Schema({
  _id: {
    type: String,
    default: generateBrandId
  },
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 100,
    unique: true,
    index: true
  },
  description: {
    type: String,
    trim: true,
    maxlength: 1000
  },
  logo: {
    url: String,
    altText: String
  },
  website: {
    type: String,
    trim: true,
    validate: {
      validator: function(v) {
        return !v || /^https?:\/\/.+/.test(v);
      },
      message: 'Website must be a valid URL'
    }
  },
  contactInfo: {
    email: {
      type: String,
      trim: true,
      lowercase: true,
      validate: {
        validator: function(v) {
          return !v || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(v);
        },
        message: 'Invalid email format'
      }
    },
    phone: {
      type: String,
      trim: true
    },
    address: {
      street: String,
      city: String,
      state: String,
      country: String,
      zipCode: String
    }
  },
  socialMedia: {
    facebook: String,
    twitter: String,
    instagram: String,
    linkedin: String
  },
  categories: [{
    categoryId: {
      type: String,
      required: true
    },
    categoryName: {
      type: String,
      required: true
    }
  }],
  status: {
    type: String,
    required: true,
    enum: ['active', 'inactive', 'pending', 'suspended'],
    default: 'active',
    index: true
  },
  featured: {
    type: Boolean,
    default: false,
    index: true
  },
  establishedYear: {
    type: Number,
    min: 1800,
    max: new Date().getFullYear()
  },
  country: {
    type: String,
    trim: true,
    maxlength: 50
  },
  tags: [{
    type: String,
    trim: true,
    lowercase: true
  }],
  seo: {
    metaTitle: String,
    metaDescription: String,
    slug: {
      type: String,
      unique: true,
      sparse: true,
      lowercase: true
    }
  },
  statistics: {
    totalProducts: {
      type: Number,
      default: 0,
      min: 0
    },
    averageRating: {
      type: Number,
      default: 0,
      min: 0,
      max: 5
    },
    totalReviews: {
      type: Number,
      default: 0,
      min: 0
    }
  },
  createdBy: {
    type: String,
    required: true
  },
  updatedBy: {
    type: String
  }
}, {
  timestamps: true,
  collection: 'brands'
});

// Indexes for better query performance
brandSchema.index({ name: 'text', description: 'text' });
brandSchema.index({ status: 1, featured: 1 });
brandSchema.index({ 'categories.categoryId': 1 });
brandSchema.index({ country: 1 });
brandSchema.index({ createdAt: -1 });

// Pre-save middleware to generate slug
brandSchema.pre('save', function(next) {
  if (this.isModified('name') && !this.seo.slug) {
    this.seo.slug = this.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/^-+|-+$/g, '');
  }
  next();
});

// Static method to find active brands
brandSchema.statics.findActive = function(options = {}) {
  return this.find({
    status: 'active',
    ...options
  });
};

// Static method to find featured brands
brandSchema.statics.findFeatured = function(options = {}) {
  return this.find({
    status: 'active',
    featured: true,
    ...options
  });
};

// Method to update product statistics
brandSchema.methods.updateStatistics = async function() {
  const Product = mongoose.model('Product');
  
  const stats = await Product.aggregate([
    {
      $match: {
        'brand.brandId': this._id,
        status: 'active'
      }
    },
    {
      $group: {
        _id: null,
        totalProducts: { $sum: 1 },
        averageRating: { $avg: '$ratings.averageRating' },
        totalReviews: { $sum: '$ratings.totalReviews' }
      }
    }
  ]);
  
  if (stats.length > 0) {
    this.statistics.totalProducts = stats[0].totalProducts || 0;
    this.statistics.averageRating = Math.round((stats[0].averageRating || 0) * 10) / 10;
    this.statistics.totalReviews = stats[0].totalReviews || 0;
  } else {
    this.statistics.totalProducts = 0;
    this.statistics.averageRating = 0;
    this.statistics.totalReviews = 0;
  }
  
  return this.save();
};

const Brand = mongoose.model('Brand', brandSchema);

module.exports = Brand;
