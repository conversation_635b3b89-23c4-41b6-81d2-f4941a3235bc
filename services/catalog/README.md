# Catalog Service

A comprehensive catalog management microservice for handling products, brands, categories, deals, and seller price configurations.

## Features

- **Product Management**: Complete CRUD operations for products with inventory tracking
- **Brand Management**: Brand creation and management with statistics
- **Category Management**: Hierarchical category structure with tree operations
- **Deal Management**: Flexible deal system with multiple discount types
- **Seller Price Configuration**: Seller-specific pricing with audit logs
- **Seller Deal Management**: Seller-specific deal configurations

## API Endpoints

### Products
- `POST /products` - Create a new product
- `GET /products` - Get all products with pagination and filtering
- `GET /products/:id` - Get product by ID
- `PUT /products/:id` - Update product
- `DELETE /products/:id` - Delete product
- `GET /products/category/:categoryId` - Get products by category
- `GET /products/brand/:brandId` - Get products by brand

### Brands
- `POST /brands` - Create a new brand
- `GET /brands` - Get all brands with pagination and filtering
- `GET /brands/:id` - Get brand by ID
- `PUT /brands/:id` - Update brand
- `DELETE /brands/:id` - Delete brand
- `GET /brands/active` - Get active brands

### Categories
- `POST /categories` - Create a new category
- `GET /categories` - Get all categories with pagination and filtering
- `GET /categories/:id` - Get category by ID
- `PUT /categories/:id` - Update category
- `DELETE /categories/:id` - Delete category
- `GET /categories/tree` - Get category tree/hierarchy
- `GET /categories/:id/subcategories` - Get subcategories

### Deals
- `POST /deals` - Create a new deal
- `GET /deals` - Get all deals with pagination and filtering
- `GET /deals/:id` - Get deal by ID
- `PUT /deals/:id` - Update deal
- `DELETE /deals/:id` - Delete deal
- `GET /deals/active` - Get active deals

### Seller Price Config
- `POST /seller-price-config` - Create a new seller price config
- `GET /seller-price-config` - Get all configs with pagination and filtering
- `GET /seller-price-config/:id` - Get config by ID
- `PUT /seller-price-config/:id` - Update config
- `DELETE /seller-price-config/:id` - Delete config
- `GET /seller-price-config/seller/:sellerId` - Get configs by seller
- `GET /seller-price-config/product/:productId` - Get configs by product

## Models

### Product
- Comprehensive product information with SKU, pricing, inventory
- Category and brand associations
- Image management and SEO optimization
- Specifications and attributes

### Brand
- Brand information with contact details and social media
- Category associations and statistics
- Featured brand support

### Category
- Hierarchical category structure with unlimited levels
- Path-based organization for efficient queries
- Category attributes and SEO optimization

### Deal
- Flexible deal types (percentage, fixed amount, BOGO, bundle)
- Product, category, and brand applicability
- Usage limits and priority system
- Time-based activation

### SellerPriceConfig
- Seller-specific pricing configurations
- Inventory management per seller
- Commission calculations
- Approval workflow

### SellerDeal
- Seller-specific deal configurations
- Revenue tracking and commission management
- Approval workflow for seller deals

### SellerPriceConfigLogs
- Complete audit trail for price configuration changes
- Change tracking with before/after values
- User attribution and metadata

## Installation

1. Navigate to the catalog service directory:
   ```bash
   cd services/catalog
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables (copy from user service and modify as needed)

4. Start the service:
   ```bash
   npm start
   ```

## Development

- `npm run dev` - Start with nodemon for development
- `npm test` - Run tests
- `npm run pm2:start` - Start with PM2

## Environment Variables

- `PORT` - Service port (default: 5002)
- `NODE_ENV` - Environment (development/production)
- `MONGODB_URI` - MongoDB connection string
- `FRONTEND_URL` - Frontend application URL

## Database Indexes

The service includes optimized indexes for:
- Text search on products, brands, and categories
- Category hierarchy queries
- Deal applicability lookups
- Seller and product associations
- Audit trail queries

## Features

### Product Management
- SKU-based product identification
- Multi-image support with primary image selection
- Inventory tracking with low stock alerts
- SEO-friendly URLs and metadata

### Category Hierarchy
- Unlimited category depth
- Path-based efficient queries
- Automatic statistics updates
- Circular reference prevention

### Deal System
- Multiple discount types
- Stackable deals support
- Usage and time limits
- Priority-based application

### Seller Integration
- Seller-specific pricing
- Commission calculations
- Inventory management per seller
- Complete audit trail

## Error Handling

The service includes comprehensive error handling with:
- Validation errors for all inputs
- Duplicate key error handling
- Not found errors with appropriate messages
- Database connection error handling
