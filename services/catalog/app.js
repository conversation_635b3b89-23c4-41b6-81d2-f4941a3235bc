const express = require('express');
const serverless = require('serverless-http');
const cors = require('cors');
const morgan = require('morgan');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const path = require('path');

// Load environment variables with explicit path
require('dotenv').config({ path: path.join(__dirname, '.env') });

// Import middleware
const database = require('./middleware/database');
const { generateRequestId, requestLogger, errorLogger, performanceMonitor, securityHeaders, requestTimeout } = require('./middleware/requestLogger');
const { globalErrorHandler, notFoundHandler } = require('./middleware/errorHandler');
const healthCheck = require('./middleware/healthCheck');


// Configuration
const PORT = process.env.PORT || 5002;
const NODE_ENV = process.env.NODE_ENV || 'development';
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for API
  crossOriginEmbedderPolicy: false
}));

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // In development, allow all origins
    if (process.env.NODE_ENV === 'development') {
      callback(null, true);
      return;
    }

    const allowedOrigins = [
      FRONTEND_URL,
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8000', // API Gateway
      'http://localhost:5000'  // API Gateway alternative
    ];

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept', 'Origin']
};

app.use(cors(corsOptions));

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS) || 100, // limit each IP to 100 requests per windowMs
  message: {
    success: false,
    error: 'Too many requests',
    message: 'Please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
});

app.use('/api/', limiter);

// Request parsing middleware
app.use(express.json({
  limit: process.env.REQUEST_SIZE_LIMIT || '10mb',
  strict: true
}));
app.use(express.urlencoded({
  limit: process.env.REQUEST_SIZE_LIMIT || '10mb',
  extended: true
}));

// Custom middleware
app.use(generateRequestId);
app.use(securityHeaders);
app.use(requestTimeout(30000)); // 30 second timeout
app.use(performanceMonitor);

// Logging middleware
if (NODE_ENV === 'development') {
  app.use(morgan('dev'));
}
app.use(requestLogger);

// Health check endpoints
app.get('/health', healthCheck.healthCheck);
app.get('/health/detailed', healthCheck.detailedHealthCheck);
app.get('/health/ready', healthCheck.readinessProbe);
app.get('/health/live', healthCheck.livenessProbe);
app.get('/metrics', healthCheck.metrics);

app.get('/', (req, res, next) => {
  req.url = '/catalog';
  next();
});

// POST handler for root endpoint
app.post('/', (req, res, next) => {
  req.url = '/catalog';
  next();
});

// PUT handler for root endpoint
app.put('/', (req, res, next) => {
  req.url = '/catalog';
  next();
});

// API routes
app.use('/deals', require('./routes/deals'));
app.use('/products', require('./routes/products'));
app.use('/brands', require('./routes/brands'));
app.use('/categories', require('./routes/categories'));
app.use('/seller-price-config', require('./routes/sellerPriceConfig'));

// 404 handler
app.use('*', notFoundHandler);

// Error handling middleware
app.use(errorLogger);
app.use(globalErrorHandler);

// Database connection and server startup
const startServer = () => {
  database.connect()
    .then(() => {
      // Start server
      app.listen(PORT, () => {
        console.log(`🚀 Catalog Service running on port ${PORT}`);
        console.log(`📊 Environment: ${NODE_ENV}`);
        console.log(`🔗 Health check: http://localhost:${PORT}/health`);
      });
    })
    .catch((error) => {
      console.error('❌ Failed to start server:', error);
      process.exit(1);
    });
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 SIGTERM received, shutting down gracefully...');
  database.disconnect()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
});

process.on('SIGINT', () => {
  console.log('🛑 SIGINT received, shutting down gracefully...');
  database.disconnect()
    .then(() => process.exit(0))
    .catch(() => process.exit(1));
});

// Start the server
if (require.main === module) {
  startServer();
}

// Export for serverless deployment
module.exports = app;
module.exports.handler = serverless(app);
