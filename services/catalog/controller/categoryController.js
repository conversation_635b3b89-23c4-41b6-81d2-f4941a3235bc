const categoryService = require('../services/categoryService');

/**
 * Category Controller
 * Handles HTTP requests for category management operations
 */

/**
 * Create a new category
 * POST /categories
 */
const createCategory = async (req, res, next) => {
  try {
    const categoryData = req.body;
    const result = await categoryService.createCategory(categoryData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.category,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all categories with pagination and filtering
 * GET /categories
 */
const getCategories = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, parentId, search } = req.query;
    const filters = { status, parentId, search };
    
    const result = await categoryService.getCategories(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Categories retrieved successfully',
      data: result.categories,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get category by ID
 * GET /categories/:id
 */
const getCategoryById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await categoryService.getCategoryById(id);

    res.status(200).json({
      success: true,
      message: 'Category retrieved successfully',
      data: result.category,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update category by ID
 * PUT /categories/:id
 */
const updateCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await categoryService.updateCategory(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.category,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete category by ID
 * DELETE /categories/:id
 */
const deleteCategory = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await categoryService.deleteCategory(id);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get category tree/hierarchy
 * GET /categories/tree
 */
const getCategoryTree = async (req, res, next) => {
  try {
    const result = await categoryService.getCategoryTree();

    res.status(200).json({
      success: true,
      message: 'Category tree retrieved successfully',
      data: result.categories,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get subcategories by parent ID
 * GET /categories/:id/subcategories
 */
const getSubcategories = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const result = await categoryService.getSubcategories(id, page, limit);

    res.status(200).json({
      success: true,
      message: 'Subcategories retrieved successfully',
      data: result.categories,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoryTree,
  getSubcategories
};
