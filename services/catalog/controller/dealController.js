const dealService = require('../services/dealService');

/**
 * Deal Controller
 * Handles HTTP requests for deal management operations
 */

/**
 * Create a new deal
 * POST /deals
 */
const createDeal = async (req, res, next) => {
  try {
    const dealData = req.body;
    const result = await dealService.createDeal(dealData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.deal,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all deals with pagination and filtering
 * GET /deals
 */
const getDeals = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, category, brand } = req.query;
    const filters = { status, category, brand };
    
    const result = await dealService.getDeals(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Deals retrieved successfully',
      data: result.deals,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get deal by ID
 * GET /deals/:id
 */
const getDealById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await dealService.getDealById(id);

    res.status(200).json({
      success: true,
      message: 'Deal retrieved successfully',
      data: result.deal,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update deal by ID
 * PUT /deals/:id
 */
const updateDeal = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await dealService.updateDeal(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.deal,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete deal by ID
 * DELETE /deals/:id
 */
const deleteDeal = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await dealService.deleteDeal(id);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get active deals
 * GET /deals/active
 */
const getActiveDeals = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const result = await dealService.getActiveDeals(page, limit);

    res.status(200).json({
      success: true,
      message: 'Active deals retrieved successfully',
      data: result.deals,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createDeal,
  getDeals,
  getDealById,
  updateDeal,
  deleteDeal,
  getActiveDeals
};
