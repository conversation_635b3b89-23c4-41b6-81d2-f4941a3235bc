const sellerPriceConfigService = require('../services/sellerPriceConfigService');

/**
 * Seller Price Config Controller
 * Handles HTTP requests for seller price configuration management operations
 */

/**
 * Create a new seller price config
 * POST /seller-price-config
 */
const createSellerPriceConfig = async (req, res, next) => {
  try {
    const configData = req.body;
    const result = await sellerPriceConfigService.createSellerPriceConfig(configData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.config,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all seller price configs with pagination and filtering
 * GET /seller-price-config
 */
const getSellerPriceConfigs = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, sellerId, productId, status } = req.query;
    const filters = { sellerId, productId, status };
    
    const result = await sellerPriceConfigService.getSellerPriceConfigs(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Seller price configs retrieved successfully',
      data: result.configs,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get seller price config by ID
 * GET /seller-price-config/:id
 */
const getSellerPriceConfigById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await sellerPriceConfigService.getSellerPriceConfigById(id);

    res.status(200).json({
      success: true,
      message: 'Seller price config retrieved successfully',
      data: result.config,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update seller price config by ID
 * PUT /seller-price-config/:id
 */
const updateSellerPriceConfig = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await sellerPriceConfigService.updateSellerPriceConfig(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.config,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete seller price config by ID
 * DELETE /seller-price-config/:id
 */
const deleteSellerPriceConfig = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await sellerPriceConfigService.deleteSellerPriceConfig(id);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get seller price configs by seller ID
 * GET /seller-price-config/seller/:sellerId
 */
const getConfigsBySeller = async (req, res, next) => {
  try {
    const { sellerId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const result = await sellerPriceConfigService.getConfigsBySeller(sellerId, page, limit);

    res.status(200).json({
      success: true,
      message: 'Seller price configs retrieved successfully',
      data: result.configs,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get seller price configs by product ID
 * GET /seller-price-config/product/:productId
 */
const getConfigsByProduct = async (req, res, next) => {
  try {
    const { productId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const result = await sellerPriceConfigService.getConfigsByProduct(productId, page, limit);

    res.status(200).json({
      success: true,
      message: 'Seller price configs retrieved successfully',
      data: result.configs,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createSellerPriceConfig,
  getSellerPriceConfigs,
  getSellerPriceConfigById,
  updateSellerPriceConfig,
  deleteSellerPriceConfig,
  getConfigsBySeller,
  getConfigsByProduct
};
