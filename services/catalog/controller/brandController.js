const brandService = require('../services/brandService');

/**
 * Brand Controller
 * Handles HTTP requests for brand management operations
 */

/**
 * Create a new brand
 * POST /brands
 */
const createBrand = async (req, res, next) => {
  try {
    const brandData = req.body;
    const result = await brandService.createBrand(brandData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all brands with pagination and filtering
 * GET /brands
 */
const getBrands = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, status, search } = req.query;
    const filters = { status, search };
    
    const result = await brandService.getBrands(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Brands retrieved successfully',
      data: result.brands,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get brand by ID
 * GET /brands/:id
 */
const getBrandById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await brandService.getBrandById(id);

    res.status(200).json({
      success: true,
      message: 'Brand retrieved successfully',
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update brand by ID
 * PUT /brands/:id
 */
const updateBrand = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await brandService.updateBrand(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.brand,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete brand by ID
 * DELETE /brands/:id
 */
const deleteBrand = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await brandService.deleteBrand(id);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get active brands
 * GET /brands/active
 */
const getActiveBrands = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const result = await brandService.getActiveBrands(page, limit);

    res.status(200).json({
      success: true,
      message: 'Active brands retrieved successfully',
      data: result.brands,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createBrand,
  getBrands,
  getBrandById,
  updateBrand,
  deleteBrand,
  getActiveBrands
};
