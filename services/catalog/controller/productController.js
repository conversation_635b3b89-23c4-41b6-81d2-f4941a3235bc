const productService = require('../services/productService');

/**
 * Product Controller
 * Handles HTTP requests for product management operations
 */

/**
 * Create a new product
 * POST /products
 */
const createProduct = async (req, res, next) => {
  try {
    const productData = req.body;
    const result = await productService.createProduct(productData);

    res.status(201).json({
      success: true,
      message: result.message,
      data: result.product,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get all products with pagination and filtering
 * GET /products
 */
const getProducts = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, category, brand, status, search } = req.query;
    const filters = { category, brand, status, search };
    
    const result = await productService.getProducts(page, limit, filters);

    res.status(200).json({
      success: true,
      message: 'Products retrieved successfully',
      data: result.products,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get product by ID
 * GET /products/:id
 */
const getProductById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await productService.getProductById(id);

    res.status(200).json({
      success: true,
      message: 'Product retrieved successfully',
      data: result.product,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update product by ID
 * PUT /products/:id
 */
const updateProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;
    const result = await productService.updateProduct(id, updateData);

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.product,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete product by ID
 * DELETE /products/:id
 */
const deleteProduct = async (req, res, next) => {
  try {
    const { id } = req.params;
    const result = await productService.deleteProduct(id);

    res.status(200).json({
      success: true,
      message: result.message,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get products by category
 * GET /products/category/:categoryId
 */
const getProductsByCategory = async (req, res, next) => {
  try {
    const { categoryId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const result = await productService.getProductsByCategory(categoryId, page, limit);

    res.status(200).json({
      success: true,
      message: 'Products retrieved successfully',
      data: result.products,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get products by brand
 * GET /products/brand/:brandId
 */
const getProductsByBrand = async (req, res, next) => {
  try {
    const { brandId } = req.params;
    const { page = 1, limit = 10 } = req.query;
    const result = await productService.getProductsByBrand(brandId, page, limit);

    res.status(200).json({
      success: true,
      message: 'Products retrieved successfully',
      data: result.products,
      pagination: result.pagination,
      requestId: req.requestId,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  createProduct,
  getProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  getProductsByCategory,
  getProductsByBrand
};
