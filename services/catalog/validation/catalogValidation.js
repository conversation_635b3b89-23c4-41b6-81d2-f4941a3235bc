const Joi = require('joi');

/**
 * Validation schemas for catalog service
 */

// Product validation schema
const productSchema = Joi.object({
  name: Joi.string().trim().max(200).required(),
  description: Joi.string().trim().max(2000).required(),
  shortDescription: Joi.string().trim().max(500),
  sku: Joi.string().trim().uppercase().required(),
  barcode: Joi.string().trim(),
  category: Joi.object({
    categoryId: Joi.string().required(),
    categoryName: Joi.string().required(),
    categoryPath: Joi.string().required()
  }).required(),
  brand: Joi.object({
    brandId: Joi.string().required(),
    brandName: Joi.string().required()
  }).required(),
  pricing: Joi.object({
    basePrice: Joi.number().min(0).required(),
    sellingPrice: Joi.number().min(0).required(),
    costPrice: Joi.number().min(0),
    currency: Joi.string().valid('INR', 'USD', 'EUR').default('INR')
  }).required(),
  inventory: Joi.object({
    stockQuantity: Joi.number().min(0).default(0),
    lowStockThreshold: Joi.number().min(0).default(10),
    trackInventory: Joi.boolean().default(true)
  }),
  specifications: Joi.object({
    weight: Joi.object({
      value: Joi.number().min(0),
      unit: Joi.string().valid('kg', 'g', 'lb', 'oz').default('kg')
    }),
    dimensions: Joi.object({
      length: Joi.number().min(0),
      width: Joi.number().min(0),
      height: Joi.number().min(0),
      unit: Joi.string().valid('cm', 'inch', 'm').default('cm')
    }),
    color: Joi.string(),
    size: Joi.string(),
    material: Joi.string(),
    model: Joi.string()
  }),
  images: Joi.array().items(Joi.object({
    url: Joi.string().uri().required(),
    altText: Joi.string(),
    isPrimary: Joi.boolean().default(false),
    order: Joi.number().default(0)
  })),
  tags: Joi.array().items(Joi.string().trim().lowercase()),
  status: Joi.string().valid('draft', 'active', 'inactive', 'discontinued').default('draft'),
  visibility: Joi.string().valid('public', 'private', 'hidden').default('public'),
  createdBy: Joi.string().required()
});

// Brand validation schema
const brandSchema = Joi.object({
  name: Joi.string().trim().max(100).required(),
  description: Joi.string().trim().max(1000),
  logo: Joi.object({
    url: Joi.string().uri(),
    altText: Joi.string()
  }),
  website: Joi.string().uri(),
  contactInfo: Joi.object({
    email: Joi.string().email(),
    phone: Joi.string(),
    address: Joi.object({
      street: Joi.string(),
      city: Joi.string(),
      state: Joi.string(),
      country: Joi.string(),
      zipCode: Joi.string()
    })
  }),
  socialMedia: Joi.object({
    facebook: Joi.string(),
    twitter: Joi.string(),
    instagram: Joi.string(),
    linkedin: Joi.string()
  }),
  categories: Joi.array().items(Joi.object({
    categoryId: Joi.string().required(),
    categoryName: Joi.string().required()
  })),
  status: Joi.string().valid('active', 'inactive', 'pending', 'suspended').default('active'),
  featured: Joi.boolean().default(false),
  establishedYear: Joi.number().min(1800).max(new Date().getFullYear()),
  country: Joi.string().trim().max(50),
  tags: Joi.array().items(Joi.string().trim().lowercase()),
  createdBy: Joi.string().required()
});

// Category validation schema
const categorySchema = Joi.object({
  name: Joi.string().trim().max(100).required(),
  description: Joi.string().trim().max(1000),
  parentId: Joi.string().allow(null),
  image: Joi.object({
    url: Joi.string().uri(),
    altText: Joi.string()
  }),
  icon: Joi.string().trim(),
  status: Joi.string().valid('active', 'inactive', 'draft').default('active'),
  featured: Joi.boolean().default(false),
  sortOrder: Joi.number().default(0),
  attributes: Joi.array().items(Joi.object({
    name: Joi.string().trim().required(),
    type: Joi.string().valid('text', 'number', 'boolean', 'select', 'multiselect').default('text'),
    required: Joi.boolean().default(false),
    options: Joi.array().items(Joi.string()),
    unit: Joi.string()
  })),
  createdBy: Joi.string().required()
});

// Deal validation schema
const dealSchema = Joi.object({
  title: Joi.string().trim().max(200).required(),
  description: Joi.string().trim().max(1000).required(),
  dealType: Joi.string().valid('percentage', 'fixed_amount', 'buy_one_get_one', 'bundle').default('percentage'),
  discountValue: Joi.number().min(0).required(),
  minimumOrderValue: Joi.number().min(0).default(0),
  maximumDiscountAmount: Joi.number().min(0),
  startDate: Joi.date().required(),
  endDate: Joi.date().greater(Joi.ref('startDate')).required(),
  status: Joi.string().valid('draft', 'active', 'paused', 'expired', 'cancelled').default('draft'),
  applicableProducts: Joi.array().items(Joi.object({
    productId: Joi.string().required(),
    productName: Joi.string().required()
  })),
  applicableCategories: Joi.array().items(Joi.object({
    categoryId: Joi.string().required(),
    categoryName: Joi.string().required()
  })),
  applicableBrands: Joi.array().items(Joi.object({
    brandId: Joi.string().required(),
    brandName: Joi.string().required()
  })),
  usageLimit: Joi.number().min(1),
  userLimit: Joi.number().min(1),
  isStackable: Joi.boolean().default(false),
  priority: Joi.number().min(1).max(10).default(1),
  terms: Joi.string().max(2000),
  createdBy: Joi.string().required()
});

// Seller Price Config validation schema
const sellerPriceConfigSchema = Joi.object({
  sellerId: Joi.string().required(),
  sellerName: Joi.string().trim().required(),
  productId: Joi.string().required(),
  productName: Joi.string().trim().required(),
  sku: Joi.string().trim().uppercase().required(),
  pricing: Joi.object({
    basePrice: Joi.number().min(0).required(),
    sellerPrice: Joi.number().min(0).required(),
    costPrice: Joi.number().min(0),
    currency: Joi.string().valid('INR', 'USD', 'EUR').default('INR')
  }).required(),
  inventory: Joi.object({
    stockQuantity: Joi.number().min(0).default(0),
    lowStockThreshold: Joi.number().min(0).default(10),
    maxOrderQuantity: Joi.number().min(1),
    minOrderQuantity: Joi.number().min(1).default(1)
  }),
  commission: Joi.object({
    type: Joi.string().valid('percentage', 'fixed').default('percentage'),
    value: Joi.number().min(0).required()
  }).required(),
  shipping: Joi.object({
    weight: Joi.object({
      value: Joi.number().min(0),
      unit: Joi.string().valid('kg', 'g', 'lb', 'oz').default('kg')
    }),
    dimensions: Joi.object({
      length: Joi.number().min(0),
      width: Joi.number().min(0),
      height: Joi.number().min(0),
      unit: Joi.string().valid('cm', 'inch', 'm').default('cm')
    }),
    shippingCost: Joi.number().min(0).default(0),
    freeShippingThreshold: Joi.number().min(0)
  }),
  status: Joi.string().valid('pending', 'approved', 'active', 'inactive', 'rejected', 'suspended').default('pending'),
  effectiveDate: Joi.date().default(Date.now),
  expiryDate: Joi.date(),
  priority: Joi.number().min(1).max(10).default(1),
  tags: Joi.array().items(Joi.string().trim().lowercase()),
  notes: Joi.string().max(1000),
  createdBy: Joi.string().required()
});

module.exports = {
  productSchema,
  brandSchema,
  categorySchema,
  dealSchema,
  sellerPriceConfigSchema
};
