const Deal = require('../models/Deal');

/**
 * Deal Service
 * Business logic for deal management operations
 */

/**
 * Create a new deal
 */
const createDeal = async (dealData) => {
  try {
    const deal = new Deal(dealData);
    await deal.save();

    return {
      success: true,
      message: 'Deal created successfully',
      deal: deal
    };
  } catch (error) {
    if (error.code === 11000) {
      throw new Error('Deal with this identifier already exists');
    }
    throw error;
  }
};

/**
 * Get deals with pagination and filtering
 */
const getDeals = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.category) {
      query['applicableCategories.categoryId'] = filters.category;
    }
    if (filters.brand) {
      query['applicableBrands.brandId'] = filters.brand;
    }

    const deals = await Deal.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Deal.countDocuments(query);

    return {
      success: true,
      deals: deals,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get deal by ID
 */
const getDealById = async (dealId) => {
  try {
    const deal = await Deal.findById(dealId);

    if (!deal) {
      throw new Error('Deal not found');
    }

    return {
      success: true,
      deal: deal
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update deal by ID
 */
const updateDeal = async (dealId, updateData) => {
  try {
    const deal = await Deal.findByIdAndUpdate(
      dealId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!deal) {
      throw new Error('Deal not found');
    }

    return {
      success: true,
      message: 'Deal updated successfully',
      deal: deal
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Delete deal by ID
 */
const deleteDeal = async (dealId) => {
  try {
    const deal = await Deal.findByIdAndDelete(dealId);

    if (!deal) {
      throw new Error('Deal not found');
    }

    return {
      success: true,
      message: 'Deal deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get active deals
 */
const getActiveDeals = async (page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;
    const now = new Date();

    const deals = await Deal.find({
      status: 'active',
      startDate: { $lte: now },
      endDate: { $gte: now }
    })
      .sort({ priority: -1, createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Deal.countDocuments({
      status: 'active',
      startDate: { $lte: now },
      endDate: { $gte: now }
    });

    return {
      success: true,
      deals: deals,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get deals applicable to a product
 */
const getDealsForProduct = async (productId, categoryId, brandId) => {
  try {
    const now = new Date();
    
    const deals = await Deal.find({
      status: 'active',
      startDate: { $lte: now },
      endDate: { $gte: now },
      $or: [
        { 'applicableProducts.productId': productId },
        { 'applicableCategories.categoryId': categoryId },
        { 'applicableBrands.brandId': brandId }
      ]
    }).sort({ priority: -1 });

    return {
      success: true,
      deals: deals
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createDeal,
  getDeals,
  getDealById,
  updateDeal,
  deleteDeal,
  getActiveDeals,
  getDealsForProduct
};
