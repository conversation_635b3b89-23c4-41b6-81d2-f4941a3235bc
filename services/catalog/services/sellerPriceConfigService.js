const SellerPriceConfig = require('../models/SellerPriceConfig');
const SellerPriceConfigLogs = require('../models/SellerPriceConfigLogs');

/**
 * Seller Price Config Service
 * Business logic for seller price configuration management operations
 */

/**
 * Create a new seller price config
 */
const createSellerPriceConfig = async (configData) => {
  try {
    const config = new SellerPriceConfig(configData);
    await config.save();

    // Create log entry
    await SellerPriceConfigLogs.createLog({
      configId: config._id,
      sellerId: config.sellerId,
      sellerName: config.sellerName,
      productId: config.productId,
      productName: config.productName,
      action: 'created',
      changes: {
        field: 'all',
        oldValue: null,
        newValue: config.toObject(),
        changeType: 'create'
      },
      currentData: {
        pricing: config.pricing,
        inventory: config.inventory,
        commission: config.commission,
        status: config.status
      },
      performedBy: {
        userId: configData.createdBy,
        userName: configData.createdBy,
        userType: 'admin'
      }
    });

    return {
      success: true,
      message: 'Seller price config created successfully',
      config: config
    };
  } catch (error) {
    if (error.code === 11000) {
      throw new Error('Price config for this seller and product already exists');
    }
    throw error;
  }
};

/**
 * Get seller price configs with pagination and filtering
 */
const getSellerPriceConfigs = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.sellerId) {
      query.sellerId = filters.sellerId;
    }
    if (filters.productId) {
      query.productId = filters.productId;
    }
    if (filters.status) {
      query.status = filters.status;
    }

    const configs = await SellerPriceConfig.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await SellerPriceConfig.countDocuments(query);

    return {
      success: true,
      configs: configs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get seller price config by ID
 */
const getSellerPriceConfigById = async (configId) => {
  try {
    const config = await SellerPriceConfig.findById(configId);

    if (!config) {
      throw new Error('Seller price config not found');
    }

    return {
      success: true,
      config: config
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update seller price config by ID
 */
const updateSellerPriceConfig = async (configId, updateData) => {
  try {
    const oldConfig = await SellerPriceConfig.findById(configId);
    if (!oldConfig) {
      throw new Error('Seller price config not found');
    }

    const config = await SellerPriceConfig.findByIdAndUpdate(
      configId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    // Create log entry for the update
    await SellerPriceConfigLogs.createLog({
      configId: config._id,
      sellerId: config.sellerId,
      sellerName: config.sellerName,
      productId: config.productId,
      productName: config.productName,
      action: 'updated',
      changes: {
        field: 'multiple',
        oldValue: oldConfig.toObject(),
        newValue: config.toObject(),
        changeType: 'update'
      },
      previousData: {
        pricing: oldConfig.pricing,
        inventory: oldConfig.inventory,
        commission: oldConfig.commission,
        status: oldConfig.status
      },
      currentData: {
        pricing: config.pricing,
        inventory: config.inventory,
        commission: config.commission,
        status: config.status
      },
      performedBy: {
        userId: updateData.updatedBy || 'system',
        userName: updateData.updatedBy || 'system',
        userType: 'admin'
      }
    });

    return {
      success: true,
      message: 'Seller price config updated successfully',
      config: config
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Delete seller price config by ID
 */
const deleteSellerPriceConfig = async (configId) => {
  try {
    const config = await SellerPriceConfig.findByIdAndDelete(configId);

    if (!config) {
      throw new Error('Seller price config not found');
    }

    // Create log entry for deletion
    await SellerPriceConfigLogs.createLog({
      configId: config._id,
      sellerId: config.sellerId,
      sellerName: config.sellerName,
      productId: config.productId,
      productName: config.productName,
      action: 'deleted',
      changes: {
        field: 'all',
        oldValue: config.toObject(),
        newValue: null,
        changeType: 'delete'
      },
      previousData: {
        pricing: config.pricing,
        inventory: config.inventory,
        commission: config.commission,
        status: config.status
      },
      performedBy: {
        userId: 'system',
        userName: 'system',
        userType: 'admin'
      }
    });

    return {
      success: true,
      message: 'Seller price config deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get configs by seller ID
 */
const getConfigsBySeller = async (sellerId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const configs = await SellerPriceConfig.findActiveBySeller(sellerId)
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await SellerPriceConfig.countDocuments({
      sellerId: sellerId,
      status: 'active'
    });

    return {
      success: true,
      configs: configs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get configs by product ID
 */
const getConfigsByProduct = async (productId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const configs = await SellerPriceConfig.findByProduct(productId, true)
      .skip(skip)
      .limit(parseInt(limit));

    const total = await SellerPriceConfig.countDocuments({
      productId: productId,
      status: 'active'
    });

    return {
      success: true,
      configs: configs,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createSellerPriceConfig,
  getSellerPriceConfigs,
  getSellerPriceConfigById,
  updateSellerPriceConfig,
  deleteSellerPriceConfig,
  getConfigsBySeller,
  getConfigsByProduct
};
