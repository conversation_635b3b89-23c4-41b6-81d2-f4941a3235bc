const Product = require('../models/Product');

/**
 * Product Service
 * Business logic for product management operations
 */

/**
 * Create a new product
 */
const createProduct = async (productData) => {
  try {
    const product = new Product(productData);
    await product.save();

    return {
      success: true,
      message: 'Product created successfully',
      product: product
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.sku) {
        throw new Error('Product with this SKU already exists');
      }
      if (error.keyPattern['seo.slug']) {
        throw new Error('Product with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Get products with pagination and filtering
 */
const getProducts = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.category) {
      query['category.categoryId'] = filters.category;
    }
    if (filters.brand) {
      query['brand.brandId'] = filters.brand;
    }
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    const products = await Product.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Product.countDocuments(query);

    return {
      success: true,
      products: products,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get product by ID
 */
const getProductById = async (productId) => {
  try {
    const product = await Product.findById(productId);

    if (!product) {
      throw new Error('Product not found');
    }

    return {
      success: true,
      product: product
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update product by ID
 */
const updateProduct = async (productId, updateData) => {
  try {
    const product = await Product.findByIdAndUpdate(
      productId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!product) {
      throw new Error('Product not found');
    }

    return {
      success: true,
      message: 'Product updated successfully',
      product: product
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.sku) {
        throw new Error('Product with this SKU already exists');
      }
      if (error.keyPattern['seo.slug']) {
        throw new Error('Product with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Delete product by ID
 */
const deleteProduct = async (productId) => {
  try {
    const product = await Product.findByIdAndDelete(productId);

    if (!product) {
      throw new Error('Product not found');
    }

    return {
      success: true,
      message: 'Product deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get products by category
 */
const getProductsByCategory = async (categoryId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const products = await Product.findByCategory(categoryId)
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await Product.countDocuments({
      'category.categoryId': categoryId,
      status: 'active',
      visibility: 'public'
    });

    return {
      success: true,
      products: products,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get products by brand
 */
const getProductsByBrand = async (brandId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const products = await Product.findByBrand(brandId)
      .skip(skip)
      .limit(parseInt(limit))
      .sort({ createdAt: -1 });

    const total = await Product.countDocuments({
      'brand.brandId': brandId,
      status: 'active',
      visibility: 'public'
    });

    return {
      success: true,
      products: products,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update product inventory
 */
const updateProductInventory = async (productId, stockQuantity) => {
  try {
    const product = await Product.findByIdAndUpdate(
      productId,
      { 
        'inventory.stockQuantity': stockQuantity,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    );

    if (!product) {
      throw new Error('Product not found');
    }

    return {
      success: true,
      message: 'Product inventory updated successfully',
      product: product
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createProduct,
  getProducts,
  getProductById,
  updateProduct,
  deleteProduct,
  getProductsByCategory,
  getProductsByBrand,
  updateProductInventory
};
