const Category = require('../models/Category');

/**
 * Category Service
 * Business logic for category management operations
 */

/**
 * Create a new category
 */
const createCategory = async (categoryData) => {
  try {
    const category = new Category(categoryData);
    await category.save();

    return {
      success: true,
      message: 'Category created successfully',
      category: category
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern['seo.slug']) {
        throw new Error('Category with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Get categories with pagination and filtering
 */
const getCategories = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.parentId !== undefined) {
      query.parentId = filters.parentId;
    }
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    const categories = await Category.find(query)
      .sort({ level: 1, sortOrder: 1, name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Category.countDocuments(query);

    return {
      success: true,
      categories: categories,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get category by ID
 */
const getCategoryById = async (categoryId) => {
  try {
    const category = await Category.findById(categoryId);

    if (!category) {
      throw new Error('Category not found');
    }

    return {
      success: true,
      category: category
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update category by ID
 */
const updateCategory = async (categoryId, updateData) => {
  try {
    const category = await Category.findByIdAndUpdate(
      categoryId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!category) {
      throw new Error('Category not found');
    }

    return {
      success: true,
      message: 'Category updated successfully',
      category: category
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern['seo.slug']) {
        throw new Error('Category with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Delete category by ID
 */
const deleteCategory = async (categoryId) => {
  try {
    // Check if category has subcategories
    const subcategoriesCount = await Category.countDocuments({
      parentId: categoryId,
      status: 'active'
    });

    if (subcategoriesCount > 0) {
      throw new Error('Cannot delete category with active subcategories');
    }

    const category = await Category.findByIdAndDelete(categoryId);

    if (!category) {
      throw new Error('Category not found');
    }

    return {
      success: true,
      message: 'Category deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get category tree
 */
const getCategoryTree = async (maxLevel = 3) => {
  try {
    const categories = await Category.getCategoryTree(null, maxLevel);

    return {
      success: true,
      categories: categories
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get subcategories by parent ID
 */
const getSubcategories = async (parentId, page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const categories = await Category.find({
      parentId: parentId,
      status: 'active'
    })
      .sort({ sortOrder: 1, name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Category.countDocuments({
      parentId: parentId,
      status: 'active'
    });

    return {
      success: true,
      categories: categories,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get category ancestors (breadcrumb)
 */
const getCategoryAncestors = async (categoryId) => {
  try {
    const ancestors = await Category.findAncestors(categoryId);

    return {
      success: true,
      ancestors: ancestors
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get category descendants
 */
const getCategoryDescendants = async (categoryId) => {
  try {
    const descendants = await Category.findDescendants(categoryId);

    return {
      success: true,
      descendants: descendants
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createCategory,
  getCategories,
  getCategoryById,
  updateCategory,
  deleteCategory,
  getCategoryTree,
  getSubcategories,
  getCategoryAncestors,
  getCategoryDescendants
};
