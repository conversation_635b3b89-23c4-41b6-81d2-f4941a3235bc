const Brand = require('../models/Brand');

/**
 * Brand Service
 * Business logic for brand management operations
 */

/**
 * Create a new brand
 */
const createBrand = async (brandData) => {
  try {
    const brand = new Brand(brandData);
    await brand.save();

    return {
      success: true,
      message: 'Brand created successfully',
      brand: brand
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.name) {
        throw new Error('Brand with this name already exists');
      }
      if (error.keyPattern['seo.slug']) {
        throw new Error('Brand with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Get brands with pagination and filtering
 */
const getBrands = async (page = 1, limit = 10, filters = {}) => {
  try {
    const skip = (page - 1) * limit;
    const query = {};

    // Apply filters
    if (filters.status) {
      query.status = filters.status;
    }
    if (filters.search) {
      query.$text = { $search: filters.search };
    }

    const brands = await Brand.find(query)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Brand.countDocuments(query);

    return {
      success: true,
      brands: brands,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get brand by ID
 */
const getBrandById = async (brandId) => {
  try {
    const brand = await Brand.findById(brandId);

    if (!brand) {
      throw new Error('Brand not found');
    }

    return {
      success: true,
      brand: brand
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Update brand by ID
 */
const updateBrand = async (brandId, updateData) => {
  try {
    const brand = await Brand.findByIdAndUpdate(
      brandId,
      { ...updateData, updatedAt: new Date() },
      { new: true, runValidators: true }
    );

    if (!brand) {
      throw new Error('Brand not found');
    }

    return {
      success: true,
      message: 'Brand updated successfully',
      brand: brand
    };
  } catch (error) {
    if (error.code === 11000) {
      if (error.keyPattern.name) {
        throw new Error('Brand with this name already exists');
      }
      if (error.keyPattern['seo.slug']) {
        throw new Error('Brand with this slug already exists');
      }
    }
    throw error;
  }
};

/**
 * Delete brand by ID
 */
const deleteBrand = async (brandId) => {
  try {
    const brand = await Brand.findByIdAndDelete(brandId);

    if (!brand) {
      throw new Error('Brand not found');
    }

    return {
      success: true,
      message: 'Brand deleted successfully'
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get active brands
 */
const getActiveBrands = async (page = 1, limit = 10) => {
  try {
    const skip = (page - 1) * limit;

    const brands = await Brand.findActive()
      .sort({ name: 1 })
      .skip(skip)
      .limit(parseInt(limit));

    const total = await Brand.countDocuments({ status: 'active' });

    return {
      success: true,
      brands: brands,
      pagination: {
        currentPage: parseInt(page),
        totalPages: Math.ceil(total / limit),
        totalItems: total,
        itemsPerPage: parseInt(limit)
      }
    };
  } catch (error) {
    throw error;
  }
};

/**
 * Get featured brands
 */
const getFeaturedBrands = async (limit = 10) => {
  try {
    const brands = await Brand.findFeatured()
      .sort({ name: 1 })
      .limit(parseInt(limit));

    return {
      success: true,
      brands: brands
    };
  } catch (error) {
    throw error;
  }
};

module.exports = {
  createBrand,
  getBrands,
  getBrandById,
  updateBrand,
  deleteBrand,
  getActiveBrands,
  getFeaturedBrands
};
