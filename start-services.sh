#!/bin/bash

# SKStore Microservices Startup Script
# This script helps you manage all your Node.js services with PM2

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  SKStore Microservices Manager${NC}"
    echo -e "${BLUE}================================${NC}"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        print_error "PM2 is not installed. Installing PM2..."
        npm install -g pm2
    else
        print_status "PM2 is already installed"
    fi
}

# Install dependencies for all services
install_dependencies() {
    print_status "Installing dependencies for all services..."
    npm run install:all
    print_status "Dependencies installed successfully"
}

# Start all services
start_services() {
    print_status "Starting all services with PM2..."
    pm2 start ecosystem.config.js
    print_status "All services started successfully"
}

# Show service status
show_status() {
    print_status "Current service status:"
    pm2 status
}

# Show logs
show_logs() {
    print_status "Showing logs for all services (Press Ctrl+C to exit):"
    pm2 logs
}

# Health check
health_check() {
    print_status "Performing health checks..."
    
    echo "API Gateway (Port 8000):"
    curl -s http://localhost:8000/ || print_warning "API Gateway health check failed"
    
    echo -e "\nUser Service (Port 5001):"
    curl -s http://localhost:5001/health | jq '.' || print_warning "User Service health check failed"
}

# Stop all services
stop_services() {
    print_status "Stopping all services..."
    pm2 stop all
    print_status "All services stopped"
}

# Restart all services
restart_services() {
    print_status "Restarting all services..."
    pm2 restart all
    print_status "All services restarted"
}

# Delete all services
delete_services() {
    print_status "Deleting all services..."
    pm2 delete all
    print_status "All services deleted"
}

# Save PM2 configuration
save_config() {
    print_status "Saving PM2 configuration..."
    pm2 save
    print_status "PM2 configuration saved"
}

# Setup startup script
setup_startup() {
    print_status "Setting up PM2 startup script..."
    pm2 startup
    print_warning "Please run the command shown above with sudo to complete startup setup"
}

# Main menu
show_menu() {
    print_header
    echo "Choose an option:"
    echo "1) Start all services"
    echo "2) Stop all services"
    echo "3) Restart all services"
    echo "4) Show service status"
    echo "5) Show logs"
    echo "6) Health check"
    echo "7) Install dependencies"
    echo "8) Save PM2 configuration"
    echo "9) Setup startup script"
    echo "10) Delete all services"
    echo "0) Exit"
    echo
}

# Handle command line arguments
case "${1:-}" in
    "start")
        print_header
        check_pm2
        start_services
        show_status
        ;;
    "stop")
        print_header
        stop_services
        ;;
    "restart")
        print_header
        restart_services
        show_status
        ;;
    "status")
        print_header
        show_status
        ;;
    "logs")
        print_header
        show_logs
        ;;
    "health")
        print_header
        health_check
        ;;
    "install")
        print_header
        install_dependencies
        ;;
    "save")
        print_header
        save_config
        ;;
    "setup")
        print_header
        setup_startup
        ;;
    "delete")
        print_header
        delete_services
        ;;
    *)
        # Interactive mode
        while true; do
            show_menu
            read -p "Enter your choice [0-10]: " choice
            case $choice in
                1)
                    check_pm2
                    start_services
                    show_status
                    ;;
                2)
                    stop_services
                    ;;
                3)
                    restart_services
                    show_status
                    ;;
                4)
                    show_status
                    ;;
                5)
                    show_logs
                    ;;
                6)
                    health_check
                    ;;
                7)
                    install_dependencies
                    ;;
                8)
                    save_config
                    ;;
                9)
                    setup_startup
                    ;;
                10)
                    delete_services
                    ;;
                0)
                    print_status "Goodbye!"
                    exit 0
                    ;;
                *)
                    print_error "Invalid option. Please try again."
                    ;;
            esac
            echo
            read -p "Press Enter to continue..."
        done
        ;;
esac
