# API Gateway Configuration

# Server Configuration
PORT=8000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production-make-it-long-and-random
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_SECRET=your-refresh-token-secret-also-change-in-production

# Service URLs (can be overridden for different environments)
USER_SERVICE_URL=http://localhost:5001
EMPLOYEE_SERVICE_URL=http://localhost:5002
FRANCHISE_SERVICE_URL=http://localhost:5003

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
REQUEST_TIMEOUT=5000

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000
AUTH_RATE_LIMIT_MAX=5

# Circuit Breaker Configuration
CIRCUIT_BREAKER_FAILURE_THRESHOLD=5
CIRCUIT_BREAKER_RECOVERY_TIMEOUT=60000
CIRCUIT_BREAKER_MONITORING_PERIOD=120000

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json

# Security Configuration
ENABLE_HELMET=true
ENABLE_COMPRESSION=true
MAX_REQUEST_SIZE=10mb

# Database Configuration (if needed for session storage, etc.)
# REDIS_URL=redis://localhost:6379
# MONGODB_URL=mongodb://localhost:27017/api-gateway

# Monitoring and Metrics (optional)
# METRICS_ENABLED=true
# METRICS_PORT=9090

# SSL Configuration (for production)
# SSL_CERT_PATH=/path/to/cert.pem
# SSL_KEY_PATH=/path/to/key.pem
# FORCE_HTTPS=true
