const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

/**
 * Service Registry and Dynamic Routing
 * Manages service endpoints, load balancing, and configuration-driven routing
 */

class ServiceRegistry {
  constructor() {
    this.services = new Map();
    this.loadBalancers = new Map();
    this.routingConfig = new Map();
    this.healthCheckInterval = 30000; // 30 seconds
    this.requestTimeout = 5000; // 5 seconds
    
    this.loadBalancingStrategies = {
      ROUND_ROBIN: 'round_robin',
      LEAST_CONNECTIONS: 'least_connections',
      WEIGHTED: 'weighted',
      RANDOM: 'random'
    };
    
    this.initializeDefaultServices();
  }

  /**
   * Initialize default services from configuration
   */
  initializeDefaultServices() {
    const defaultServices = [
      {
        name: 'user-service',
        instances: [
          { url: 'http://localhost:5001', weight: 1, healthy: true }
        ],
        routes: ['/user', '/api/user'],
        loadBalancing: this.loadBalancingStrategies.ROUND_ROBIN,
        healthCheck: '/health',
        timeout: 5000,
        retries: 3
      },
      {
        name: 'vendor-service',
        instances: [
          { url: 'http://localhost:5002', weight: 1, healthy: true }
        ],
        routes: ['/vendor', '/api/vendor'],
        loadBalancing: this.loadBalancingStrategies.ROUND_ROBIN,
        healthCheck: '/health',
        timeout: 5000,
        retries: 3
      },
      {
        name: 'franchise-service',
        instances: [
          { url: 'http://localhost:5003', weight: 1, healthy: true }
        ],
        routes: ['/franchise', '/api/franchise'],
        loadBalancing: this.loadBalancingStrategies.ROUND_ROBIN,
        healthCheck: '/health',
        timeout: 5000,
        retries: 3
      },
      {
        name: 'asset',
        instances: [
          { url: 'http://localhost:5004', weight: 1, healthy: true }
        ],
        routes: ['/assets', '/api/assets'],
        loadBalancing: this.loadBalancingStrategies.ROUND_ROBIN,
        healthCheck: '/health',
        timeout: 30000, // Longer timeout for file uploads
        retries: 2
      }
    ];

    defaultServices.forEach(service => {
      this.registerService(service);
    });
  }

  /**
   * Register a new service
   */
  registerService(serviceConfig) {
    const {
      name,
      instances,
      routes,
      loadBalancing = this.loadBalancingStrategies.ROUND_ROBIN,
      healthCheck = '/health',
      timeout = 5000,
      retries = 3
    } = serviceConfig;

    // Validate instances
    const validInstances = instances.map(instance => ({
      id: uuidv4(),
      url: instance.url,
      weight: instance.weight || 1,
      healthy: true,
      connections: 0,
      totalRequests: 0,
      successfulRequests: 0,
      lastHealthCheck: null,
      responseTime: null
    }));

    const service = {
      name,
      instances: validInstances,
      routes,
      loadBalancing,
      healthCheck,
      timeout,
      retries,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    this.services.set(name, service);
    this.loadBalancers.set(name, { currentIndex: 0 });

    // Register routes
    routes.forEach(route => {
      this.routingConfig.set(route, name);
    });

    console.log(`Service registered: ${name} with ${instances.length} instance(s)`);
    return service;
  }

  /**
   * Unregister a service
   */
  unregisterService(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) return false;

    // Remove routes
    service.routes.forEach(route => {
      this.routingConfig.delete(route);
    });

    this.services.delete(serviceName);
    this.loadBalancers.delete(serviceName);

    console.log(`Service unregistered: ${serviceName}`);
    return true;
  }

  /**
   * Add instance to existing service
   */
  addServiceInstance(serviceName, instanceConfig) {
    const service = this.services.get(serviceName);
    if (!service) return false;

    const instance = {
      id: uuidv4(),
      url: instanceConfig.url,
      weight: instanceConfig.weight || 1,
      healthy: true,
      connections: 0,
      totalRequests: 0,
      successfulRequests: 0,
      lastHealthCheck: null,
      responseTime: null
    };

    service.instances.push(instance);
    service.updatedAt = new Date().toISOString();

    console.log(`Instance added to ${serviceName}: ${instance.url}`);
    return instance;
  }

  /**
   * Remove instance from service
   */
  removeServiceInstance(serviceName, instanceId) {
    const service = this.services.get(serviceName);
    if (!service) return false;

    const initialLength = service.instances.length;
    service.instances = service.instances.filter(instance => instance.id !== instanceId);
    
    if (service.instances.length < initialLength) {
      service.updatedAt = new Date().toISOString();
      console.log(`Instance removed from ${serviceName}: ${instanceId}`);
      return true;
    }

    return false;
  }

  /**
   * Get service by route
   */
  getServiceByRoute(route) {
    // Find exact match first
    let serviceName = this.routingConfig.get(route);
    
    if (!serviceName) {
      // Find prefix match
      for (const [configRoute, configServiceName] of this.routingConfig.entries()) {
        if (route.startsWith(configRoute + '/') || route === configRoute) {
          serviceName = configServiceName;
          break;
        }
      }
    }

    return serviceName ? this.services.get(serviceName) : null;
  }

  /**
   * Get next available instance using load balancing strategy
   */
  getNextInstance(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) return null;

    const healthyInstances = service.instances.filter(instance => instance.healthy);
    if (healthyInstances.length === 0) return null;

    const loadBalancer = this.loadBalancers.get(serviceName);
    
    switch (service.loadBalancing) {
      case this.loadBalancingStrategies.ROUND_ROBIN:
        return this.roundRobinSelection(healthyInstances, loadBalancer);
      
      case this.loadBalancingStrategies.LEAST_CONNECTIONS:
        return this.leastConnectionsSelection(healthyInstances);
      
      case this.loadBalancingStrategies.WEIGHTED:
        return this.weightedSelection(healthyInstances);
      
      case this.loadBalancingStrategies.RANDOM:
        return this.randomSelection(healthyInstances);
      
      default:
        return this.roundRobinSelection(healthyInstances, loadBalancer);
    }
  }

  /**
   * Round robin load balancing
   */
  roundRobinSelection(instances, loadBalancer) {
    const instance = instances[loadBalancer.currentIndex % instances.length];
    loadBalancer.currentIndex = (loadBalancer.currentIndex + 1) % instances.length;
    return instance;
  }

  /**
   * Least connections load balancing
   */
  leastConnectionsSelection(instances) {
    return instances.reduce((min, current) => 
      current.connections < min.connections ? current : min
    );
  }

  /**
   * Weighted load balancing
   */
  weightedSelection(instances) {
    const totalWeight = instances.reduce((sum, instance) => sum + instance.weight, 0);
    let random = Math.random() * totalWeight;
    
    for (const instance of instances) {
      random -= instance.weight;
      if (random <= 0) {
        return instance;
      }
    }
    
    return instances[0]; // Fallback
  }

  /**
   * Random load balancing
   */
  randomSelection(instances) {
    const randomIndex = Math.floor(Math.random() * instances.length);
    return instances[randomIndex];
  }

  /**
   * Record request start
   */
  recordRequestStart(serviceName, instanceId) {
    const service = this.services.get(serviceName);
    if (!service) return;

    const instance = service.instances.find(inst => inst.id === instanceId);
    if (instance) {
      instance.connections++;
      instance.totalRequests++;
    }
  }

  /**
   * Record request end
   */
  recordRequestEnd(serviceName, instanceId, success = true) {
    const service = this.services.get(serviceName);
    if (!service) return;

    const instance = service.instances.find(inst => inst.id === instanceId);
    if (instance) {
      instance.connections = Math.max(0, instance.connections - 1);
      if (success) {
        instance.successfulRequests++;
      }
    }
  }

  /**
   * Update instance health status
   */
  updateInstanceHealth(serviceName, instanceId, healthy, responseTime = null) {
    const service = this.services.get(serviceName);
    if (!service) return;

    const instance = service.instances.find(inst => inst.id === instanceId);
    if (instance) {
      instance.healthy = healthy;
      instance.lastHealthCheck = new Date().toISOString();
      if (responseTime !== null) {
        instance.responseTime = responseTime;
      }
    }
  }

  /**
   * Get all services
   */
  getAllServices() {
    const services = {};
    for (const [name, service] of this.services.entries()) {
      services[name] = {
        ...service,
        stats: this.getServiceStats(name)
      };
    }
    return services;
  }

  /**
   * Get service statistics
   */
  getServiceStats(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) return null;

    const totalRequests = service.instances.reduce((sum, inst) => sum + inst.totalRequests, 0);
    const successfulRequests = service.instances.reduce((sum, inst) => sum + inst.successfulRequests, 0);
    const healthyInstances = service.instances.filter(inst => inst.healthy).length;

    return {
      totalInstances: service.instances.length,
      healthyInstances,
      totalRequests,
      successfulRequests,
      successRate: totalRequests > 0 ? ((successfulRequests / totalRequests) * 100).toFixed(2) + '%' : '0%',
      averageResponseTime: this.calculateAverageResponseTime(service.instances)
    };
  }

  /**
   * Calculate average response time
   */
  calculateAverageResponseTime(instances) {
    const validResponseTimes = instances
      .map(inst => inst.responseTime)
      .filter(time => time !== null);
    
    if (validResponseTimes.length === 0) return null;
    
    const average = validResponseTimes.reduce((sum, time) => sum + time, 0) / validResponseTimes.length;
    return Math.round(average);
  }

  /**
   * Get routing configuration
   */
  getRoutingConfig() {
    const config = {};
    for (const [route, serviceName] of this.routingConfig.entries()) {
      config[route] = serviceName;
    }
    return config;
  }

  /**
   * Update service configuration
   */
  updateServiceConfig(serviceName, updates) {
    const service = this.services.get(serviceName);
    if (!service) return false;

    // Update allowed fields
    const allowedUpdates = ['timeout', 'retries', 'loadBalancing', 'healthCheck'];
    allowedUpdates.forEach(field => {
      if (updates[field] !== undefined) {
        service[field] = updates[field];
      }
    });

    service.updatedAt = new Date().toISOString();
    console.log(`Service configuration updated: ${serviceName}`);
    return true;
  }
}

// Create singleton instance
const serviceRegistry = new ServiceRegistry();

module.exports = serviceRegistry;
