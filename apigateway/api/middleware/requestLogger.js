const morgan = require('morgan');
const { v4: uuidv4 } = require('uuid');

/**
 * Enhanced Request Logging Middleware
 * Provides comprehensive logging with request IDs, timing, and structured output
 */

class RequestLogger {
  constructor() {
    this.setupMorganTokens();
  }

  /**
   * Setup custom Morgan tokens
   */
  setupMorganTokens() {
    // Request ID token
    morgan.token('requestId', (req) => req.requestId);
    
    // Service name token
    morgan.token('serviceName', (req) => req.serviceName || 'gateway');
    
    // User ID token (from JWT)
    morgan.token('userId', (req) => req.user?.id || 'anonymous');
    
    // Request body size
    morgan.token('requestSize', (req) => {
      return req.get('content-length') || '0';
    });
    
    // Response time in milliseconds with precision
    morgan.token('responseTimeMs', (req, res) => {
      if (!req._startTime || !Array.isArray(req._startTime)) return '0';
      const diff = process.hrtime(req._startTime);
      return (diff[0] * 1000 + diff[1] * 1e-6).toFixed(2);
    });
    
    // Timestamp in ISO format
    morgan.token('timestamp', () => new Date().toISOString());
    
    // Client IP (considering proxies)
    morgan.token('clientIp', (req) => {
      return req.ip || 
             req.connection?.remoteAddress || 
             req.socket?.remoteAddress ||
             req.headers['x-forwarded-for']?.split(',')[0] ||
             'unknown';
    });
    
    // User agent
    morgan.token('userAgent', (req) => req.get('User-Agent') || 'unknown');
    
    // Request protocol
    morgan.token('protocol', (req) => req.protocol);
    
    // Host header
    morgan.token('host', (req) => req.get('host') || 'unknown');
  }

  /**
   * Middleware to add request ID and start time
   */
  addRequestId() {
    return (req, res, next) => {
      // Generate unique request ID
      req.requestId = uuidv4();
      
      // Add request start time for precise timing
      req._startTime = process.hrtime();
      
      // Add request ID to response headers for client tracking
      res.set('X-Request-ID', req.requestId);
      
      next();
    };
  }

  /**
   * Development logging format
   */
  developmentFormat() {
    return morgan((tokens, req, res) => {
      const log = {
        timestamp: tokens.timestamp(req, res),
        requestId: tokens.requestId(req, res),
        method: tokens.method(req, res),
        url: tokens.url(req, res),
        status: tokens.status(req, res),
        responseTime: tokens.responseTimeMs(req, res) + 'ms',
        contentLength: tokens.res(req, res, 'content-length') || '0',
        userAgent: tokens.userAgent(req, res),
        clientIp: tokens.clientIp(req, res)
      };

      // Color coding for console output
      const statusCode = parseInt(tokens.status(req, res));
      let color = '\x1b[0m'; // Reset
      
      if (statusCode >= 500) color = '\x1b[31m'; // Red
      else if (statusCode >= 400) color = '\x1b[33m'; // Yellow
      else if (statusCode >= 300) color = '\x1b[36m'; // Cyan
      else if (statusCode >= 200) color = '\x1b[32m'; // Green

      console.log(`${color}[${log.timestamp}] ${log.requestId} ${log.method} ${log.url} ${log.status} ${log.responseTime}\x1b[0m`);
      
      return null; // Don't output to Morgan's default logger
    });
  }

  /**
   * Production logging format (JSON)
   */
  productionFormat() {
    return morgan((tokens, req, res) => {
      const log = {
        timestamp: tokens.timestamp(req, res),
        level: 'info',
        type: 'access',
        requestId: tokens.requestId(req, res),
        serviceName: tokens.serviceName(req, res),
        userId: tokens.userId(req, res),
        request: {
          method: tokens.method(req, res),
          url: tokens.url(req, res),
          protocol: tokens.protocol(req, res),
          host: tokens.host(req, res),
          userAgent: tokens.userAgent(req, res),
          clientIp: tokens.clientIp(req, res),
          size: tokens.requestSize(req, res)
        },
        response: {
          status: parseInt(tokens.status(req, res)),
          size: tokens.res(req, res, 'content-length') || '0',
          time: parseFloat(tokens.responseTimeMs(req, res))
        }
      };

      // Add error details for failed requests
      if (log.response.status >= 400) {
        log.level = 'error';
        log.error = {
          statusCode: log.response.status,
          message: res.locals.errorMessage || 'Request failed'
        };
      }

      console.log(JSON.stringify(log));
      return null;
    });
  }

  /**
   * Custom format for specific use cases
   */
  customFormat(format) {
    return morgan(format);
  }

  /**
   * Skip logging for health check endpoints
   */
  skipHealthChecks() {
    return (req, res) => {
      return req.url.includes('/health') || req.url.includes('/ping');
    };
  }

  /**
   * Skip logging for static assets
   */
  skipStaticAssets() {
    return (req, res) => {
      const staticExtensions = ['.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg'];
      return staticExtensions.some(ext => req.url.endsWith(ext));
    };
  }

  /**
   * Middleware for structured error logging
   */
  errorLogger() {
    return (err, req, res, next) => {
      const errorLog = {
        timestamp: new Date().toISOString(),
        level: 'error',
        type: 'error',
        requestId: req.requestId,
        serviceName: req.serviceName || 'gateway',
        userId: req.user?.id || 'anonymous',
        error: {
          message: err.message,
          stack: err.stack,
          code: err.code,
          statusCode: err.statusCode || err.status || 500
        },
        request: {
          method: req.method,
          url: req.url,
          headers: req.headers,
          body: req.body,
          params: req.params,
          query: req.query
        }
      };

      console.error(JSON.stringify(errorLog));
      
      // Store error message for access log
      res.locals.errorMessage = err.message;
      
      next(err);
    };
  }

  /**
   * Performance monitoring middleware
   */
  performanceMonitor() {
    return (req, res, next) => {
      const start = process.hrtime.bigint();
      
      res.on('finish', () => {
        const end = process.hrtime.bigint();
        const duration = Number(end - start) / 1000000; // Convert to milliseconds
        
        // Log slow requests (> 1 second)
        if (duration > 1000) {
          const slowLog = {
            timestamp: new Date().toISOString(),
            level: 'warn',
            type: 'performance',
            requestId: req.requestId,
            message: 'Slow request detected',
            duration: duration.toFixed(2) + 'ms',
            request: {
              method: req.method,
              url: req.url,
              serviceName: req.serviceName
            }
          };
          
          console.warn(JSON.stringify(slowLog));
        }
      });
      
      next();
    };
  }

  /**
   * Get middleware based on environment
   */
  getMiddleware(environment = 'development') {
    const middlewares = [this.addRequestId()];
    
    if (environment === 'production') {
      middlewares.push(
        this.productionFormat(),
        this.performanceMonitor()
      );
    } else {
      middlewares.push(
        this.developmentFormat(),
        this.performanceMonitor()
      );
    }
    
    return middlewares;
  }
}

// Create singleton instance
const requestLogger = new RequestLogger();

module.exports = requestLogger;
