const axios = require('axios');
const { v4: uuidv4 } = require('uuid');

/**
 * Health Check and Service Monitoring Middleware
 * Implements circuit breaker pattern and service health monitoring
 */

class HealthCheckService {
  constructor() {
    this.services = new Map();
    this.circuitBreakers = new Map();
    this.healthCheckInterval = 30000; // 30 seconds
    this.requestTimeout = 5000; // 5 seconds
    
    // Circuit breaker configuration
    this.circuitBreakerConfig = {
      failureThreshold: 5,
      recoveryTimeout: 60000, // 1 minute
      monitoringPeriod: 120000 // 2 minutes
    };
    
    this.startHealthMonitoring();
  }

  /**
   * Register a service for health monitoring
   */
  registerService(name, url, healthEndpoint = '/health') {
    this.services.set(name, {
      name,
      url,
      healthEndpoint,
      status: 'unknown',
      lastCheck: null,
      responseTime: null,
      consecutiveFailures: 0,
      totalRequests: 0,
      successfulRequests: 0
    });

    this.circuitBreakers.set(name, {
      state: 'CLOSED', // CLOSED, OPEN, HALF_OPEN
      failures: 0,
      lastFailureTime: null,
      nextAttempt: null
    });

    console.log(`Registered service: ${name} at ${url}`);
  }

  /**
   * Check if circuit breaker allows request
   */
  isServiceAvailable(serviceName) {
    const breaker = this.circuitBreakers.get(serviceName);
    if (!breaker) return true;

    const now = Date.now();

    switch (breaker.state) {
      case 'CLOSED':
        return true;
      
      case 'OPEN':
        if (now >= breaker.nextAttempt) {
          breaker.state = 'HALF_OPEN';
          console.log(`Circuit breaker for ${serviceName} moved to HALF_OPEN`);
          return true;
        }
        return false;
      
      case 'HALF_OPEN':
        return true;
      
      default:
        return true;
    }
  }

  /**
   * Record successful request
   */
  recordSuccess(serviceName) {
    const service = this.services.get(serviceName);
    const breaker = this.circuitBreakers.get(serviceName);
    
    if (service) {
      service.totalRequests++;
      service.successfulRequests++;
      service.consecutiveFailures = 0;
    }

    if (breaker) {
      if (breaker.state === 'HALF_OPEN') {
        breaker.state = 'CLOSED';
        breaker.failures = 0;
        console.log(`Circuit breaker for ${serviceName} moved to CLOSED`);
      }
    }
  }

  /**
   * Record failed request
   */
  recordFailure(serviceName) {
    const service = this.services.get(serviceName);
    const breaker = this.circuitBreakers.get(serviceName);
    
    if (service) {
      service.totalRequests++;
      service.consecutiveFailures++;
    }

    if (breaker) {
      breaker.failures++;
      breaker.lastFailureTime = Date.now();

      if (breaker.failures >= this.circuitBreakerConfig.failureThreshold) {
        breaker.state = 'OPEN';
        breaker.nextAttempt = Date.now() + this.circuitBreakerConfig.recoveryTimeout;
        console.log(`Circuit breaker for ${serviceName} moved to OPEN`);
      }
    }
  }

  /**
   * Perform health check on a single service
   */
  async checkServiceHealth(serviceName) {
    const service = this.services.get(serviceName);
    if (!service) return null;

    const startTime = Date.now();
    
    try {
      const response = await axios.get(
        `${service.url}${service.healthEndpoint}`,
        { 
          timeout: this.requestTimeout,
          validateStatus: (status) => status < 500
        }
      );

      const responseTime = Date.now() - startTime;
      
      service.status = response.status === 200 ? 'healthy' : 'unhealthy';
      service.lastCheck = new Date().toISOString();
      service.responseTime = responseTime;

      if (response.status === 200) {
        this.recordSuccess(serviceName);
      } else {
        this.recordFailure(serviceName);
      }

      return {
        name: serviceName,
        status: service.status,
        responseTime,
        lastCheck: service.lastCheck
      };

    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      service.status = 'unhealthy';
      service.lastCheck = new Date().toISOString();
      service.responseTime = responseTime;
      
      this.recordFailure(serviceName);

      return {
        name: serviceName,
        status: 'unhealthy',
        error: error.message,
        responseTime,
        lastCheck: service.lastCheck
      };
    }
  }

  /**
   * Get health status of all services
   */
  async getAllServicesHealth() {
    const healthChecks = [];
    
    for (const serviceName of this.services.keys()) {
      const health = await this.checkServiceHealth(serviceName);
      if (health) {
        healthChecks.push(health);
      }
    }

    return healthChecks;
  }

  /**
   * Get detailed service statistics
   */
  getServiceStats() {
    const stats = {};
    
    for (const [name, service] of this.services.entries()) {
      const breaker = this.circuitBreakers.get(name);
      
      stats[name] = {
        status: service.status,
        lastCheck: service.lastCheck,
        responseTime: service.responseTime,
        consecutiveFailures: service.consecutiveFailures,
        totalRequests: service.totalRequests,
        successfulRequests: service.successfulRequests,
        successRate: service.totalRequests > 0 
          ? ((service.successfulRequests / service.totalRequests) * 100).toFixed(2) + '%'
          : '0%',
        circuitBreaker: {
          state: breaker?.state || 'UNKNOWN',
          failures: breaker?.failures || 0,
          nextAttempt: breaker?.nextAttempt
        }
      };
    }

    return stats;
  }

  /**
   * Start periodic health monitoring
   */
  startHealthMonitoring() {
    setInterval(async () => {
      for (const serviceName of this.services.keys()) {
        await this.checkServiceHealth(serviceName);
      }
    }, this.healthCheckInterval);

    console.log(`Health monitoring started with ${this.healthCheckInterval}ms interval`);
  }

  /**
   * Express middleware for circuit breaker
   */
  circuitBreakerMiddleware(serviceName) {
    return (req, res, next) => {
      if (!this.isServiceAvailable(serviceName)) {
        const requestId = req.requestId || uuidv4();
        return res.status(503).json({
          success: false,
          error: {
            requestId,
            timestamp: new Date().toISOString(),
            service: serviceName,
            message: `${serviceName} service is temporarily unavailable`,
            code: 'CIRCUIT_BREAKER_OPEN',
            statusCode: 503
          }
        });
      }
      
      req.serviceName = serviceName;
      next();
    };
  }
}

// Create singleton instance
const healthCheckService = new HealthCheckService();

module.exports = healthCheckService;
