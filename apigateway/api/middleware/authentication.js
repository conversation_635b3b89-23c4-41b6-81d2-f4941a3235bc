const jwt = require('jsonwebtoken');
const { v4: uuidv4 } = require('uuid');
const axios = require('axios');
const ErrorHandler = require('./errorHandler');

/**
 * JWT-based Authentication Middleware
 * Handles token validation and user context extraction
 */

class AuthenticationMiddleware {
  constructor() {
    this.jwtSecret = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.refreshTokenSecret = process.env.REFRESH_TOKEN_SECRET || 'your-refresh-token-secret';
    this.userServiceUrl = process.env.USER_SERVICE_URL || 'http://localhost:5001';

    console.log(" this.jwtSecret --- ",this.jwtSecret);
    console.log(" this.refreshTokenSecret --- ",this.refreshTokenSecret);
    console.log(" this.userServiceUrl --- ",this.userServiceUrl);
    console.log(" process.env.JWT_SECRET --- ",process.env.JWT_SECRET);
    console.log(" process.env.REFRESH_TOKEN_SECRET --- ",process.env.REFRESH_TOKEN_SECRET);
    console.log(" process.env.USER_SERVICE_URL --- ",process.env.USER_SERVICE_URL);

    // Validate required environment variables
    if (!process.env.JWT_SECRET) {
      console.warn('⚠️  JWT_SECRET not found in environment variables, using fallback');
    }

    if (!process.env.REFRESH_TOKEN_SECRET) {
      console.warn('⚠️  REFRESH_TOKEN_SECRET not found in environment variables, using fallback');
    }
    
    // Public routes that don't require authentication
    this.publicRoutes = [
      '/health',
      '/ping',
      '/api/user/auth/login',
      '/api/user/auth/register',
      '/api/user/auth/refresh',
      '/api/user/auth/forgot-password',
      '/api/user/auth/verify-reset-password',
      '/api/docs'
    ];
  }

  /**
   * Extract token from request headers
   */
  extractToken(req) {
    const authHeader = req.headers.authorization;
    
    if (authHeader && authHeader.startsWith('JWT ')) {
      return authHeader.substring(4);
    }
    
    // Also check for token in cookies
    if (req.cookies && req.cookies.token) {
      return req.cookies.token;
    }
    
    // Check query parameter (not recommended for production)
    if (req.query.token) {
      return req.query.token;
    }
    
    return null;
  }

  /**
   * Verify JWT token
   */
  async verifyToken(token) {
    try {
      // Verify with options to match token generation
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'api-gateway',
        audience: 'microservices'
      });
      return { success: true, decoded };
    } catch (error) {
      // If verification with issuer/audience fails, try without them for backward compatibility
      try {
        const decoded = jwt.verify(token, this.jwtSecret);
        return { success: true, decoded };
      } catch (fallbackError) {
        let errorType = 'INVALID_TOKEN';

        if (error.name === 'TokenExpiredError') {
          errorType = 'TOKEN_EXPIRED';
        } else if (error.name === 'JsonWebTokenError') {
          errorType = 'MALFORMED_TOKEN';
        }

        return {
          success: false,
          error: errorType,
          message: error.message
        };
      }
    }
  }

  /**
   * Generate JWT token
   */
  generateToken(payload) {
    return jwt.sign(payload, this.jwtSecret, { 
      expiresIn: this.jwtExpiresIn,
      issuer: 'api-gateway',
      audience: 'microservices'
    });
  }

  /**
   * Generate refresh token
   */
  generateRefreshToken(payload) {
    return jwt.sign(payload, this.refreshTokenSecret, { 
      expiresIn: '7d',
      issuer: 'api-gateway',
      audience: 'microservices'
    });
  }

  /**
   * Check if route is public
   */
  isPublicRoute(path) {
    return this.publicRoutes.some(route => {
      if (route.endsWith('*')) {
        return path.startsWith(route.slice(0, -1));
      }
      return path === route || path.startsWith(route + '/');
    });
  }

  /**
   * Fetch complete user information from User service
   */
  async fetchUserInfo(userId, token) {
    try {
      const response = await axios.get(`${this.userServiceUrl}/auth/me`, {
        headers: {
          'Authorization': `JWT ${token}`,
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      if (response.data && response.data.success) {
        return {
          success: true,
          user: response.data.data
        };
      }

      return {
        success: false,
        error: 'Failed to fetch user info'
      };
    } catch (error) {
      console.error('Error fetching user info:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Main authentication middleware
   */
  authenticate(options = {}) {
    return async (req, res, next) => {
      try {
        // Generate request ID for tracking
        req.requestId = req.requestId || uuidv4();

        // Check if route is public
        if (this.isPublicRoute(req.path)) {
          return next();
        }

        // Extract token
        const token = this.extractToken(req);
        if (!token) {
          return ErrorHandler.unauthorizedError(req.requestId, 'No token provided');
        }

        // Verify token
        const verification = await this.verifyToken(token);
        if (!verification.success) {
          return ErrorHandler.unauthorizedError(req.requestId, verification.message);
        }

        // Try to fetch complete user info from User service
        let userInfo = await this.fetchUserInfo(verification.decoded.id, token);

        if (userInfo.success) {
          // Use complete user info from User service
          req.user = {
            id: userInfo.user.id,
            userId: userInfo.user.userId,
            name: userInfo.user.name,
            email: userInfo.user.email,
            type: userInfo.user.type,
            status: userInfo.user.status,
            twoFactorEnabled: userInfo.user.twoFactorEnabled
          };
        } else {
          // Fallback to token payload
          req.user = {
            id: verification.decoded.id,
            userId: verification.decoded.userId,
            name: verification.decoded.name,
            email: verification.decoded.email,
            type: verification.decoded.type,
            status: verification.decoded.status,
            twoFactorEnabled: verification.decoded.twoFactorEnabled
          };
        }

        req.token = token;
        next();

      } catch (error) {
        console.error('Authentication error:', error);
        const errorResponse = ErrorHandler.createErrorResponse(error, req.requestId, 'authentication');
        return res.status(500).json(errorResponse);
      }
    };
  }

  /**
   * Optional authentication - doesn't fail if no token provided
   */
  optionalAuthenticate() {
    return async (req, res, next) => {
      try {
        // Generate request ID for tracking
        req.requestId = req.requestId || uuidv4();

        // Check if route is public
        if (this.isPublicRoute(req.path)) {
          return next();
        }

        // Extract token
        const token = this.extractToken(req);
        if (!token) {
          return next(); // Continue without authentication
        }

        // Verify token
        const verification = await this.verifyToken(token);
        if (!verification.success) {
          return next(); // Continue without authentication
        }

        // Try to fetch complete user info from User service
        let userInfo = await this.fetchUserInfo(verification.decoded.id, token);

        if (userInfo.success) {
          // Use complete user info from User service
          req.user = {
            id: userInfo.user.id,
            userId: userInfo.user.userId,
            name: userInfo.user.name,
            email: userInfo.user.email,
            type: userInfo.user.type,
            status: userInfo.user.status,
            twoFactorEnabled: userInfo.user.twoFactorEnabled
          };
        } else {
          // Fallback to token payload
          req.user = {
            id: verification.decoded.id,
            userId: verification.decoded.userId,
            name: verification.decoded.name,
            email: verification.decoded.email,
            type: verification.decoded.type,
            status: verification.decoded.status,
            twoFactorEnabled: verification.decoded.twoFactorEnabled
          };
        }

        req.token = token;
        next();

      } catch (error) {
        console.error('Optional authentication error:', error);
        next(); // Continue without authentication
      }
    };
  }

  /**
   * Require admin access
   */
  requireAdmin() {
    return (req, res, next) => {
      if (!req.user) {
        return ErrorHandler.unauthorizedError(req.requestId, 'Authentication required');
      }

      if (req.user.type !== 'admin') {
        return ErrorHandler.forbiddenError(req.requestId, 'Admin access required');
      }

      next();
    };
  }

  /**
   * Refresh token endpoint
   */
  async refreshToken(req, res) {
    try {
      const refreshToken = req.body.refreshToken || req.cookies?.refreshToken;
      
      if (!refreshToken) {
        return ErrorHandler.badRequestError(req.requestId, 'Refresh token required');
      }

      // Verify refresh token
      const decoded = jwt.verify(refreshToken, this.refreshTokenSecret);
      
      // Generate new access token
      const newToken = this.generateToken({
        id: decoded.id,
        userId: decoded.userId,
        name: decoded.name,
        email: decoded.email,
        type: decoded.type,
        status: decoded.status
      });

      // Generate new refresh token
      const newRefreshToken = this.generateRefreshToken({
        id: decoded.id,
        userId: decoded.userId,
        name: decoded.name,
        email: decoded.email,
        type: decoded.type,
        status: decoded.status
      });

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          token: newToken,
          refreshToken: newRefreshToken,
          expiresIn: this.jwtExpiresIn
        },
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Token refresh error:', error);
      return ErrorHandler.unauthorizedError(req.requestId, 'Invalid refresh token');
    }
  }

  /**
   * Logout endpoint
   */
  logout(req, res) {
    try {
      const token = req.token;
      
      if (token) {
        // Call user service to logout
        axios.post(`${this.userServiceUrl}/auth/logout`, {}, {
          headers: {
            'Authorization': `JWT ${token}`,
            'Content-Type': 'application/json'
          }
        }).catch(error => {
          console.error('Error calling user service logout:', error.message);
        });
      }

      res.status(200).json({
        success: true,
        message: 'Logout successful',
        requestId: req.requestId,
        timestamp: new Date().toISOString()
      });

    } catch (error) {
      console.error('Logout error:', error);
      const errorResponse = ErrorHandler.createErrorResponse(error, req.requestId, 'authentication');
      return res.status(500).json(errorResponse);
    }
  }
}

module.exports = new AuthenticationMiddleware();
