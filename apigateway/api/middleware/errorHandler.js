const { v4: uuidv4 } = require('uuid');

/**
 * Enhanced error handling middleware for API Gateway
 * Handles service unavailability, network errors, timeouts, and proxy errors
 */

class ErrorHandler {
  /**
   * Create standardized error response
   */
  static createErrorResponse(error, requestId, serviceName = 'unknown') {
    const timestamp = new Date().toISOString();
    
    // Default error structure
    const errorResponse = {
      success: false,
      error: {
        requestId,
        timestamp,
        service: serviceName,
        message: 'Internal server error',
        code: 'INTERNAL_ERROR',
        statusCode: 500
      }
    };

    // Handle different types of errors
    if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND') {
      errorResponse.error.message = `Service ${serviceName} is currently unavailable`;
      errorResponse.error.code = 'SERVICE_UNAVAILABLE';
      errorResponse.error.statusCode = 503;
    } else if (error.code === 'ETIMEDOUT' || error.timeout) {
      errorResponse.error.message = `Request to ${serviceName} timed out`;
      errorResponse.error.code = 'REQUEST_TIMEOUT';
      errorResponse.error.statusCode = 504;
    } else if (error.code === 'ECONNRESET') {
      errorResponse.error.message = `Connection to ${serviceName} was reset`;
      errorResponse.error.code = 'CONNECTION_RESET';
      errorResponse.error.statusCode = 502;
    } else if (error.status || error.statusCode) {
      // HTTP errors from downstream services
      const status = error.status || error.statusCode;
      errorResponse.error.statusCode = status;
      
      if (status >= 400 && status < 500) {
        errorResponse.error.message = error.message || 'Client error';
        errorResponse.error.code = 'CLIENT_ERROR';
      } else if (status >= 500) {
        errorResponse.error.message = `${serviceName} service error`;
        errorResponse.error.code = 'SERVICE_ERROR';
      }
    } else if (error.message) {
      errorResponse.error.message = error.message;
    }

    return errorResponse;
  }

  /**
   * Express error handling middleware
   */
  static middleware() {
    return (error, req, res, next) => {
      const requestId = req.requestId || uuidv4();
      const serviceName = req.serviceName || 'unknown';
      
      // Log the error for debugging
      console.error(`[${requestId}] Error in ${serviceName}:`, {
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
        timestamp: new Date().toISOString()
      });

      const errorResponse = ErrorHandler.createErrorResponse(error, requestId, serviceName);
      
      res.status(errorResponse.error.statusCode).json(errorResponse);
    };
  }

  /**
   * Proxy error handler for express-http-proxy
   */
  static proxyErrorHandler(serviceName) {
    return (err, res, next) => {
      const requestId = res.req.requestId || uuidv4();
      
      console.error(`[${requestId}] Proxy error for ${serviceName}:`, {
        error: err.message,
        code: err.code,
        timestamp: new Date().toISOString()
      });

      const errorResponse = ErrorHandler.createErrorResponse(err, requestId, serviceName);
      
      res.status(errorResponse.error.statusCode).json(errorResponse);
    };
  }

  /**
   * Circuit breaker error handler
   */
  static circuitBreakerError(serviceName, requestId) {
    return {
      success: false,
      error: {
        requestId,
        timestamp: new Date().toISOString(),
        service: serviceName,
        message: `${serviceName} service is temporarily unavailable due to repeated failures`,
        code: 'CIRCUIT_BREAKER_OPEN',
        statusCode: 503
      }
    };
  }

  /**
   * Rate limit error handler
   */
  static rateLimitError(requestId) {
    return {
      success: false,
      error: {
        requestId,
        timestamp: new Date().toISOString(),
        message: 'Too many requests, please try again later',
        code: 'RATE_LIMIT_EXCEEDED',
        statusCode: 429
      }
    };
  }

  /**
   * Authentication error handler
   */
  static authenticationError(requestId, message = 'Authentication required') {
    return {
      success: false,
      error: {
        requestId,
        timestamp: new Date().toISOString(),
        message,
        code: 'AUTHENTICATION_REQUIRED',
        statusCode: 401
      }
    };
  }

  /**
   * Authorization error handler
   */
  static authorizationError(requestId, message = 'Insufficient permissions') {
    return {
      success: false,
      error: {
        requestId,
        timestamp: new Date().toISOString(),
        message,
        code: 'INSUFFICIENT_PERMISSIONS',
        statusCode: 403
      }
    };
  }

  /**
   * Validation error handler
   */
  static validationError(requestId, errors) {
    return {
      success: false,
      error: {
        requestId,
        timestamp: new Date().toISOString(),
        message: 'Validation failed',
        code: 'VALIDATION_ERROR',
        statusCode: 400,
        details: errors
      }
    };
  }
}

module.exports = ErrorHandler;
