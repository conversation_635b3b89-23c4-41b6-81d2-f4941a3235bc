const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const compression = require('compression');
const { v4: uuidv4 } = require('uuid');
const ErrorHandler = require('./errorHandler');

/**
 * Security and Rate Limiting Middleware
 * Implements comprehensive security measures for the API Gateway
 */

class SecurityMiddleware {
  constructor() {
    this.rateLimitStore = new Map(); // In-memory store for rate limiting
    this.suspiciousIPs = new Set(); // Track suspicious IPs
    this.blockedIPs = new Set(); // Blocked IPs
  }

  /**
   * Helmet security headers configuration
   */
  getHelmetConfig() {
    return helmet({
      // Content Security Policy
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
          connectSrc: ["'self'"],
          fontSrc: ["'self'"],
          objectSrc: ["'none'"],
          mediaSrc: ["'self'"],
          frameSrc: ["'none'"],
        },
      },
      // Cross Origin Embedder Policy
      crossOriginEmbedderPolicy: false,
      // DNS Prefetch Control
      dnsPrefetchControl: { allow: false },
      // Frame Options
      frameguard: { action: 'deny' },
      // Hide Powered By
      hidePoweredBy: true,
      // HTTP Strict Transport Security
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      },
      // IE No Open
      ieNoOpen: true,
      // No Sniff
      noSniff: true,
      // Origin Agent Cluster
      originAgentCluster: true,
      // Permitted Cross Domain Policies
      permittedCrossDomainPolicies: false,
      // Referrer Policy
      referrerPolicy: { policy: "no-referrer" },
      // X-XSS-Protection
      xssFilter: true
    });
  }

  /**
   * CORS configuration
   */
  getCorsConfig() {
    return {
      origin: (origin, callback) => {
        // Allow requests with no origin (mobile apps, etc.)
        if (!origin) return callback(null, true);
        
        // Define allowed origins
        const allowedOrigins = [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:8080',
          process.env.FRONTEND_URL
        ].filter(Boolean);

        if (allowedOrigins.includes(origin)) {
          callback(null, true);
        } else {
          callback(new Error('Not allowed by CORS'));
        }
      },
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Origin',
        'X-Requested-With',
        'Content-Type',
        'Accept',
        'Authorization',
        'X-Request-ID'
      ],
      exposedHeaders: ['X-Request-ID', 'X-RateLimit-Remaining', 'X-RateLimit-Reset']
    };
  }

  /**
   * General rate limiting
   */
  generalRateLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // Limit each IP to 1000 requests per windowMs
      message: {
        success: false,
        error: {
          message: 'Too many requests from this IP, please try again later',
          code: 'RATE_LIMIT_EXCEEDED',
          statusCode: 429
        }
      },
      standardHeaders: true,
      legacyHeaders: false,
      keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress;
      },
      handler: (req, res) => {
        const requestId = req.requestId || uuidv4();
        const errorResponse = ErrorHandler.rateLimitError(requestId);
        res.status(429).json(errorResponse);
      }
    });
  }

  /**
   * Strict rate limiting for authentication endpoints
   */
  authRateLimit() {
    return rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // Limit each IP to 5 login attempts per windowMs
      message: {
        success: false,
        error: {
          message: 'Too many authentication attempts, please try again later',
          code: 'AUTH_RATE_LIMIT_EXCEEDED',
          statusCode: 429
        }
      },
      skipSuccessfulRequests: true,
      keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress;
      }
    });
  }

  /**
   * User-based rate limiting (requires authentication)
   */
  userRateLimit() {
    return rateLimit({
      windowMs: 60 * 1000, // 1 minute
      max: 100, // Limit each user to 100 requests per minute
      keyGenerator: (req) => {
        return req.user?.id || req.ip;
      },
      skip: (req) => {
        // Skip rate limiting for admin users
        return req.user?.type === 'admin';
      }
    });
  }

  /**
   * Request size limiting
   */
  requestSizeLimit() {
    return (req, res, next) => {
      const maxSize = 10 * 1024 * 1024; // 10MB
      const contentLength = parseInt(req.get('content-length') || '0');
      
      if (contentLength > maxSize) {
        const requestId = req.requestId || uuidv4();
        return res.status(413).json({
          success: false,
          error: {
            requestId,
            timestamp: new Date().toISOString(),
            message: 'Request entity too large',
            code: 'PAYLOAD_TOO_LARGE',
            statusCode: 413,
            maxSize: `${maxSize / (1024 * 1024)}MB`
          }
        });
      }
      
      next();
    };
  }

  /**
   * IP blocking middleware
   */
  ipBlocker() {
    return (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      
      if (this.blockedIPs.has(clientIP)) {
        const requestId = req.requestId || uuidv4();
        return res.status(403).json({
          success: false,
          error: {
            requestId,
            timestamp: new Date().toISOString(),
            message: 'Access denied',
            code: 'IP_BLOCKED',
            statusCode: 403
          }
        });
      }
      
      next();
    };
  }

  /**
   * Suspicious activity detection
   */
  suspiciousActivityDetector() {
    return (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent') || '';
      
      // Check for suspicious patterns
      const suspiciousPatterns = [
        /bot/i,
        /crawler/i,
        /spider/i,
        /scraper/i,
        /hack/i,
        /exploit/i
      ];
      
      const isSuspicious = suspiciousPatterns.some(pattern => 
        pattern.test(userAgent) || pattern.test(req.url)
      );
      
      if (isSuspicious) {
        this.suspiciousIPs.add(clientIP);
        console.warn(`Suspicious activity detected from IP: ${clientIP}, User-Agent: ${userAgent}`);
      }
      
      next();
    };
  }

  /**
   * Request validation middleware
   */
  requestValidator() {
    return (req, res, next) => {
      const requestId = req.requestId || uuidv4();
      
      // Check for required headers
      if (req.method !== 'GET' && req.method !== 'HEAD') {
        if (!req.get('Content-Type')) {
          return res.status(400).json({
            success: false,
            error: {
              requestId,
              timestamp: new Date().toISOString(),
              message: 'Content-Type header is required',
              code: 'MISSING_CONTENT_TYPE',
              statusCode: 400
            }
          });
        }
      }
      
      // Validate JSON content type for POST/PUT/PATCH
      if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
        const contentType = req.get('Content-Type') || '';
        if (!contentType.includes('application/json') && !contentType.includes('multipart/form-data')) {
          return res.status(415).json({
            success: false,
            error: {
              requestId,
              timestamp: new Date().toISOString(),
              message: 'Unsupported Media Type',
              code: 'UNSUPPORTED_MEDIA_TYPE',
              statusCode: 415
            }
          });
        }
      }
      
      next();
    };
  }

  /**
   * Compression middleware
   */
  getCompression() {
    return compression({
      filter: (req, res) => {
        // Don't compress responses if the request includes a cache-control: no-transform directive
        if (req.headers['cache-control'] && req.headers['cache-control'].includes('no-transform')) {
          return false;
        }
        
        // Use compression filter function
        return compression.filter(req, res);
      },
      level: 6, // Compression level (1-9)
      threshold: 1024 // Only compress responses larger than 1KB
    });
  }

  /**
   * Security headers middleware
   */
  securityHeaders() {
    return (req, res, next) => {
      // Add custom security headers
      res.set({
        'X-API-Gateway': 'v1.0.0',
        'X-Request-ID': req.requestId,
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'DENY',
        'X-XSS-Protection': '1; mode=block',
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });
      
      next();
    };
  }

  /**
   * Get all security middleware
   */
  getAllMiddleware() {
    return [
      this.getHelmetConfig(),
      this.getCompression(),
      this.securityHeaders(),
      this.ipBlocker(),
      this.suspiciousActivityDetector(),
      this.requestSizeLimit(),
      this.requestValidator(),
      this.generalRateLimit()
    ];
  }

  /**
   * Block an IP address
   */
  blockIP(ip) {
    this.blockedIPs.add(ip);
    console.log(`IP ${ip} has been blocked`);
  }

  /**
   * Unblock an IP address
   */
  unblockIP(ip) {
    this.blockedIPs.delete(ip);
    console.log(`IP ${ip} has been unblocked`);
  }

  /**
   * Get blocked IPs
   */
  getBlockedIPs() {
    return Array.from(this.blockedIPs);
  }

  /**
   * Get suspicious IPs
   */
  getSuspiciousIPs() {
    return Array.from(this.suspiciousIPs);
  }
}

// Create singleton instance
const securityMiddleware = new SecurityMiddleware();

module.exports = securityMiddleware;
