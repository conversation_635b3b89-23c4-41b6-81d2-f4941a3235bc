# API Gateway for Microservices

A robust, production-ready API Gateway built with Node.js and Express, designed to serve as the entry point for microservices architecture.

## 🚀 Features

### Core Functionality
- **Dynamic Service Discovery**: Configuration-driven routing with service registry
- **Load Balancing**: Multiple strategies (Round Robin, Least Connections, Weighted, Random)
- **Circuit Breaker Pattern**: Automatic failure detection and recovery
- **Health Monitoring**: Real-time service health checks and monitoring

### Security & Performance
- **JWT Authentication**: Token-based authentication with refresh tokens
- **Rate Limiting**: Configurable rate limits per IP/user with different tiers
- **Security Headers**: Comprehensive security headers via Helmet
- **Request Validation**: Input validation and sanitization
- **CORS Configuration**: Flexible cross-origin resource sharing setup

### Observability
- **Request Logging**: Structured logging with unique request IDs
- **Performance Monitoring**: Response time tracking and slow request detection
- **Error Handling**: Comprehensive error handling with standardized responses
- **Metrics & Stats**: Service statistics and performance metrics

### Developer Experience
- **Hot Reloading**: Development mode with nodemon
- **Comprehensive Testing**: Unit and integration tests
- **API Documentation**: Built-in documentation endpoint
- **Environment Configuration**: Flexible configuration via environment variables

## 📋 Prerequisites

- Node.js 18+ (recommended: 22.x LTS)
- npm or yarn
- Running microservices on configured ports

## 🛠️ Installation

1. **Clone or navigate to the API Gateway directory**
   ```bash
   cd apigateway
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start the gateway**
   ```bash
   # Development mode
   npm run dev
   
   # Production mode
   npm start
   ```

## ⚙️ Configuration

### Environment Variables

Create a `.env` file based on `.env.example`:

```env
# Server Configuration
PORT=8000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
REFRESH_TOKEN_SECRET=your-refresh-token-secret

# Service URLs
USER_SERVICE_URL=http://localhost:5001
EMPLOYEE_SERVICE_URL=http://localhost:5002
FRANCHISE_SERVICE_URL=http://localhost:5003

# Frontend URL for CORS
FRONTEND_URL=http://localhost:3000
```

### Service Configuration

Services are automatically registered with default configurations. You can modify the service registry in `api/services/serviceRegistry.js`:

```javascript
const defaultServices = [
  {
    name: 'user-service',
    instances: [
      { url: 'http://localhost:5001', weight: 1, healthy: true }
    ],
    routes: ['/user', '/api/user'],
    loadBalancing: 'round_robin',
    healthCheck: '/health',
    timeout: 5000,
    retries: 3
  }
  // ... more services
];
```

## 🔌 API Endpoints

### Health Checks
- `GET /health` - Overall gateway and services health
- `GET /health/:serviceName` - Individual service health

### Authentication
- `POST /auth/refresh` - Refresh JWT token
- `POST /auth/logout` - Logout user

### Admin (Requires admin role)
- `GET /admin/services` - Service registry information
- `GET /admin/routing` - Routing configuration

### Documentation
- `GET /docs` - API documentation

### Service Proxying
- `ALL /api/user/*` - Proxies to user service
- `ALL /api/employee/*` - Proxies to employee service
- `ALL /api/franchise/*` - Proxies to franchise service

## 🏗️ Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Client App    │───▶│   API Gateway    │───▶│  Microservice   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ├─ Authentication
                              ├─ Rate Limiting
                              ├─ Load Balancing
                              ├─ Circuit Breaker
                              ├─ Health Monitoring
                              ├─ Request Logging
                              └─ Error Handling
```

### Middleware Stack

1. **Security Middleware** - Helmet, CORS, IP blocking
2. **Request Logger** - Unique request IDs, structured logging
3. **Authentication** - JWT validation (optional for public routes)
4. **Rate Limiting** - IP and user-based rate limiting
5. **Circuit Breaker** - Service availability checking
6. **Proxy** - Dynamic routing to microservices
7. **Error Handler** - Standardized error responses

## 🧪 Testing

Run the test suite:

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

### Test Structure

- **Unit Tests**: Individual middleware and utility functions
- **Integration Tests**: Full request/response cycles
- **Mock Services**: Simulated microservices for testing

## 📊 Monitoring & Observability

### Request Logging

All requests are logged with structured data:

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "level": "info",
  "type": "access",
  "requestId": "uuid-here",
  "serviceName": "user-service",
  "userId": "user123",
  "request": {
    "method": "GET",
    "url": "/user/profile",
    "clientIp": "***********",
    "userAgent": "Mozilla/5.0..."
  },
  "response": {
    "status": 200,
    "time": 45.67
  }
}
```

### Health Monitoring

- Automatic health checks every 30 seconds
- Circuit breaker pattern with configurable thresholds
- Service statistics and performance metrics

### Error Tracking

Standardized error responses with:
- Unique request IDs for tracing
- Error categorization and codes
- Detailed error context for debugging

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control
- Refresh token support
- Route-based authentication requirements

### Security Headers
- Content Security Policy
- HSTS (HTTP Strict Transport Security)
- X-Frame-Options
- X-Content-Type-Options
- And more via Helmet

### Rate Limiting
- General rate limiting: 1000 requests per 15 minutes
- Auth endpoints: 5 attempts per 15 minutes
- User-based limiting: 100 requests per minute
- Admin bypass for rate limits

## 🚀 Deployment

### Production Checklist

1. **Environment Variables**
   - Set strong JWT secrets
   - Configure proper service URLs
   - Set NODE_ENV=production

2. **Security**
   - Enable HTTPS
   - Configure proper CORS origins
   - Set up proper firewall rules

3. **Monitoring**
   - Set up log aggregation
   - Configure health check monitoring
   - Set up alerting for service failures

4. **Performance**
   - Enable compression
   - Configure proper rate limits
   - Set up load balancing if needed

### Docker Deployment

```dockerfile
FROM node:22-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 8000
CMD ["npm", "start"]
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass
6. Submit a pull request

## 📝 License

This project is licensed under the ISC License.

## 🆘 Support

For support and questions:
- Check the `/docs` endpoint for API documentation
- Review the health check endpoints for service status
- Check logs for detailed error information
- Refer to the test files for usage examples
