{"name": "api-gateway", "version": "1.0.0", "description": "Robust API Gateway for microservices architecture", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest", "test:watch": "jest --watch", "pm2:start": "pm2 start ecosystem.config.js --only api-gateway", "pm2:stop": "pm2 stop api-gateway", "pm2:restart": "pm2 restart api-gateway", "pm2:reload": "pm2 reload api-gateway", "pm2:delete": "pm2 delete api-gateway", "pm2:logs": "pm2 logs api-gateway", "pm2:monit": "pm2 monit"}, "keywords": ["api-gateway", "microservices", "express", "proxy"], "author": "", "license": "ISC", "dependencies": {"express": "^4.18.2", "express-http-proxy": "^2.0.0", "cors": "^2.8.5", "morgan": "^1.10.0", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "jsonwebtoken": "^9.0.2", "axios": "^1.6.2", "uuid": "^9.0.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}}